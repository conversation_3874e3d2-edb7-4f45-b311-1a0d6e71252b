{"name": "key-skill-set-frontend", "licence": "MIT", "version": "1.4.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts --max_old_space_size=4096 build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx ./src", "sitemap": "babel-node ./sitemap-builder.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@ckeditor/ckeditor5-alignment": "^36.0.1", "@ckeditor/ckeditor5-build-classic": "^36.0.0", "@ckeditor/ckeditor5-react": "^5.0.6", "@ckeditor/ckeditor5-table": "^36.0.1", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@faker-js/faker": "^6.1.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.0", "@iconify/react": "^3.2.1", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^5.6.2", "@mui/joy": "^5.0.0-beta.52", "@mui/lab": "^5.0.0-alpha.77", "@mui/material": "^5.8.3", "@mui/styles": "^5.6.2", "@mui/system": "^6.3.1", "@mui/x-charts": "^7.28.0", "@mui/x-date-pickers": "^5.0.3", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^1.8.1", "@testing-library/jest-dom": "^5.16.2", "@tinymce/tinymce-react": "^4.3.0", "ace-builds": "^1.14.0", "apexcharts": "^3.35.0", "apisauce": "^2.1.5", "axios": "^1.7.9", "broadcast-channel": "^4.18.1", "change-case": "^4.1.2", "ckeditor5": "^35.4.0", "classnames": "^2.3.1", "crypto-browserify": "^3.12.1", "date-fns": "^2.28.0", "dayjs": "^1.11.13", "dompurify": "^3.1.6", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "exceljs": "^4.4.0", "formik": "^2.2.9", "gapi-script": "^1.2.0", "history": "^5.2.0", "i18next": "^23.12.2", "imagekitio-react": "^2.0.0", "js-cookie": "^3.0.1", "jwt-decode": "^3.1.2", "katex": "^0.16.21", "lodash": "^4.17.21", "lottie-web": "^5.9.4", "material-ui-dropzone": "^3.5.0", "moment": "^2.29.4", "mui-datatables": "^4.2.2", "numeral": "^2.0.6", "prop-types": "^15.8.1", "quill-image-resize-module-react": "^3.0.0", "quill-paste-smart": "^2.0.0", "react": "^17.0.2", "react-ace": "^10.1.0", "react-apexcharts": "^1.4.0", "react-beautiful-dnd": "^13.1.0", "react-circular-progressbar": "^2.0.4", "react-datepicker": "^4.8.0", "react-dom": "^17.0.2", "react-draft-wysiwyg": "^1.14.7", "react-draggable": "^4.4.5", "react-email-editor": "^1.7.11", "react-gauge-chart": "^0.4.0", "react-google-login": "^5.2.2", "react-helmet-async": "^1.3.0", "react-html5video": "^2.5.1", "react-i18next": "^14.1.3", "react-icons": "^5.2.1", "react-intl": "^6.6.8", "react-joyride": "^2.5.3", "react-latex-next": "^3.0.0", "react-modal": "^3.16.1", "react-phone-input-2": "^2.15.1", "react-pro-sidebar": "^1.0.0", "react-quill": "^2.0.0", "react-redux": "^7.2.8", "react-responsive-carousel": "^3.2.23", "react-rnd": "^10.5.2", "react-router-dom": "^6.29.0", "react-scripts": "^5.0.1", "react-slick": "^0.29.0", "recharts": "^2.15.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "simplebar-react": "^2.3.6", "sweetalert": "^2.1.2", "swiper": "^6.8.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^0.30.0"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-proposal-private-property-in-object": "^7.14.5", "eslint": "^8.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.4.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "immutable": "^4.1.0", "prettier": "^2.5.1"}, "alias": {"react/jsx-runtime": "react/jsx-runtime.js"}}