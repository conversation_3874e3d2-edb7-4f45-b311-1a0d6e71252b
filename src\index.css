@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');


body[data-page="login"] header,
body[data-page="sign-up"] header {
  display: none !important;
}


body[data-page*="website-signup"] header {
  display: block !important;
}

.login-page header,
.signup-page header {
  display: none !important;
}


.website-signup-page header {
  display: block !important;
}

::-webkit-scrollbar {
    width: 12px;
}
 
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); 
    border-radius: 10px;
    
}
 
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); 
}

/* .carousel .slide img {
  height: 250px;
} */

@media screen and (max-width: 900px) and (min-width: 600px) {
  .swiper-wrapper {
    width: 250px !important;
  }
  .mySwiper .swiper-wrapper{
    height: '390px';
    padding-top: '10px';
  }
  } 

  .MuiDropzoneArea-root:not(.unique){
    min-height: 110px !important;
  }
  .unique .MuiDropzoneArea-root{
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .unique h6 + span,   .unique h6 + span > div {
    height: 195px;
    width: 100%;
  }
  .unique img {
    object-fit: contain;

  }
  .unique div[class^="makeStyles-imgPreviewRoot-"] {
   padding: 0 !important;
  }

  .MuiDropzoneArea-text{
    margin: 0 !important;
    font-size: 0.95rem !important;
    padding-top: 1rem;
  }

  .MuiDropzoneArea-icon{
    color: rgb(37 72 233 / 87%) !important;
    width: 40px !important;
  }

  .rdw-editor-main {
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    background: #f2f2f287;
    min-height: 100px;
    padding: 0 8px;
    border-radius: 4px;
    border: 1px solid #dce0e4;
}

  
.mejs-container-fullscreen {
  background-color: #fff !important;
}

.mejs-container-fullscreen .mejs-mediaelement,
.mejs-container-fullscreen video {
  height: 530px !important;
  top: 500px !important;
}

.mejs-container.mejs-container-fullscreen .mejs-controls {
  bottom: auto !important;
  top: 1000px !important;
  /* video top + video height - controls height (30px) */
}

.swal-button--confirm {
  padding: 7px 19px;
  border-radius: 2px;
  background-color: #FE7000;
  font-size: 12px;
  /* border: 1px solid #3e549a; */
  /* text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.3); */
}

.swal-button--cancel {
  padding: 7px 19px;
  border-radius: 2px;
  background-color: #acb4b1;
  font-size: 12px;
  color: rgb(81, 79, 79);
  /* border: 1px solid #3e549a; */
  /* text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.3); */
}

.achivementTextHeading{
  top: 25px;
  position: relative;
}
.wg-default .country-selector {
  bottom: 0;
  position: fixed;
  right: auto !important;
  left: 20px;
}

html {
  scroll-behavior: smooth;
}

.tox-notifications-container{
  display: none;
}

.fontdetails{
  font-family:'Poppins',
}
button[aria-label="Upload Image"], button.cRNzOE{
  display: none !important;
}

.swiper-container .swiper-button-prev{
  margin-left: 2px !important; /* I added this for carousel arrow icons fixes*/
}

#QuestionsContent > p > span{
  color: unset !important;
}

.cardviewWrapper .cardTitlenew + p, .cardviewWrapper [class*="makeStyles-cardTitlenew"] + p , .cardviewWrapper [class*="-body1"] + p[class*="-body2"] {
  position: absolute;
  top: 55%;
  line-height: 1.15;
}
.cardviewWrapper [class*=-body1]+p[class*=-body2]{
  top: 53%;
}
.cardviewWrapper [class*=-body1]+p[class*=-body2] > p{
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  font-size: 12.8px !important;
  font-weight: 400 !important;
  color: rgb(99, 115, 129) !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical;
}
.cardviewWrapper [class*=-body1]+p[class*=-body2]>p:not(:first-child) {
  display: none !important;
}
/* #moduleProgress  */
#sectionCourseView [class*="progressBox"], #sectionCourseView + div{
  border: none !important;
}
#sectionCourseView [class*="Progress"], #sectionCourseView #moduleName, #AutoplaySection,
#sectionCourseView #completedPercentage, #sectionCourseView [role="alert"], #AutoplaySection{
  display: none;
}
#sectionCourseView{
  display: flex;
  position: relative;
}
#sectionCourseView +  [class*="progressBox"] {
width: 25% !important;
border: none !important;
}
#progressBox{
  border: none !important;
}
#sidebarPanelAside #progressBox{
  padding-bottom: 0 !important;
  background-color: #fff !important;
  border-top-right-radius: 8px;
}
#completedPercentage{
      width: 100%;
    align-items: baseline;
    position: relative;
    top: 6px;
}
#sectionCourseView +  [class*="progressBox"] #AutoplaySection, #sectionCourseView +  [class*="progressBox"] #moduleName,
#sectionCourseView +  [class*="progressBox"] [role="alert"], #progressBox [role="alert"]{
  display: none !important;
}
button + #progressBox{
  border: none !important;
}
#SkillSetIQ span{
  font-size: 14px !important;
}
#SkillSetIQForVideos span{
   font-size: 14px !important;
   padding-left: 18px !important;
   line-height: 1.25 !important;
}
#curvedField > div{
   border-radius: 30px !important;
   border: none !important;
   box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px !important;
}
#progressBox [className*="LinearProgress"]{
  top: 6px !important;
}
#radioGroup label span {
    padding-top: 4px;
    padding-bottom: 4px;
}
body:not(:has(#preventZoom)) .simulationSec .Indigo image {
  position: relative !important;
}
#myMenu {
    max-height: Calc(100vh - 132px);
    overflow-y: auto;
}

@media screen and (max-width: 1050px) and(min-width: 900px){
  #profileContainer {
    margin-left: -227px !important;
  }
}

@media screen and (max-width: 900px){
  #profileContainer aside{
    width:  100% !important;
    margin-top: 90px !important;
    margin-left: 10px !important;
    margin-right: 0px !important;
    display: none !important;
  }
  #profileHeaderSection {
    margin-left: 0px !important;
  }
  h5.fontdetails {
    font-weight: 500;
    font-size: 15px;
    margin-left: 0px;
    line-height: 1.2;
    text-wrap-style: balance;
  }
  button.userProfile {
    right: 15px;
  }
  #cookiesAlert{
  flex-direction: column;
}
}


@media screen and (min-width: 600px){
  #profileContainer aside{
    margin-right: 0px !important;
  }
}

@media screen and (min-width: 1550px) {
  .cookiesMsg{
    width: Calc(100vw - 250px) ;
  }
  /* #profileHeaderSection{
    margin-left: 0px !important;
  } */
  /* #profileContainer aside{
    margin-top: 85px !important;
    width: 100% !important;

  } */
}

@media screen and (max-width: 1550px) {
  /* #profileHeaderSection{
    margin-left: 0px !important;
  }
  #profileContainer aside{
    margin-top: 85px !important;
    width: 100% !important;

  } */
};


@media screen and (max-width: 480px) and (min-width: 320px) {
  h5.fontdetails + h5.fontdetails {
    max-width: 133px;
    padding-left: 2px;
}
}

@media screen and (max-width: 600px){
  #sidebarPanelAside aside {
    position: fixed !important;
}
[className*="fixedHead"], [class*="fixedHead"]{
  margin-right: "16px !important";
  width: Calc(100% - 119px);
}
svg[data-testid="ExpandLessIcon"]{
  display: none !important;
}
#profileHeaderSection{
  flex-direction: column;
  margin-right: 0 !important;
  margin-left: 14px !important
}
#profileHeaderSection > div{
  width: 100% !important;
}
#profileContainer [class*="MuiGrid-item"]{
  padding-top: 12px !important;
  padding-right: 5px !important;
}
}
/* .element.style{
  margin-right: "-90px!important"
} */


.editor-element{
  z-index: 9 !important; 
}
/* aside + div #simulationSec .editor-element{ getting issue with element heading position
    position: relative !important;
} */
aside + div #simulationSec{
    min-height: 480px !important;
    margin-top: 12px !important;
}
aside+div #simulationSec > div:nth-child(2){
    min-height: calc(100vh - 192px) !important;
}
#menuToggle {
  display: block;
  position: fixed;
  left: 4px;
  z-index: 99;
  -webkit-user-select: none;
  user-select: none;
}
aside+div #simulationSec > div:has(video){
  display: block !important; 
} 
#menuToggle a {
  text-decoration: none;
  color: #232323;

  transition: color 0.3s ease;
}

#menuToggle a:hover {
  color: tomato;
}

#menuToggle input {
  display: block;
  width: 40px;
  height: 32px;
  position: absolute;
  top: -7px;
  left: -5px;

  cursor: pointer;

  opacity: 0; 
  z-index: 2; 

  -webkit-touch-callout: none;
}

#menuToggle span#menuSet {
  display: block;
  width: 22px;
  height: 2px;
  margin-bottom: 5px;
  position: relative;
left: 12px;
  background: #272727;
  border-radius: 3px;

  z-index: 1;

  transform-origin: 4px 0px;

  transition: transform 0.5s cubic-bezier(0.77, 0.2, 0.05, 1),
    background 0.5s cubic-bezier(0.77, 0.2, 0.05, 1), opacity 0.55s ease;
}

#menuToggle span#menuSet:first-child {
  transform-origin: 0% 0%;
}

#menuToggle span#menuSet:nth-last-child(2) {
  transform-origin: 0% 100%;
}

#menuToggle input:checked ~ span#menuSet {
  opacity: 1;
  transform: rotate(45deg) translate(-2px, -1px);
  background: #232323;
}
#menuToggle input:checked ~ span#menuSet{
    width: 26.3px !important;
}

#menuToggle input:checked ~ span#menuSet:nth-last-child(3) {
  opacity: 0;
  transform: rotate(0deg) scale(0.2, 0.2);
}


#menuToggle input:checked ~ span#menuSet:nth-last-child(2) {
  transform: rotate(-45deg) translate(0, -1px);
}

#menu {
  position: absolute;
  max-width: 350px;
  width: 100vw;
  max-height: 100vh;
  z-index: 1500;
  height: Calc(100vh - 150px);
  box-sizing: border-box;
  overflow-y: auto;
  background: #ededed;
  list-style-type: none;
  -webkit-font-smoothing: antialiased;


  transition: transform 0.5s cubic-bezier(0.77, 0.2, 0.05, 1);
}

#menu li {
  padding: 10px 0;
  font-size: 22px;
}

#menu li label {
  cursor: pointer;
}

#menuToggle input:checked ~ ul {
  transform: none;
}
#EditorContainer > div{
  max-width: 750px !important;
}
h6#TitleName{
  height: 42px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-height: 1.2;
  -webkit-box-orient: vertical;
}
#nestedListSubHeading{
  line-height: 30px !important;
  padding-left: 15px;
  font-size: 15px;
  background: "#fff";
}
#nestedListSubHeading::after {
  content: "";
  border-top: 0.1rem double #999;
  height: 0; 
  width: 100%;
}
#chipLabel + div > div > div{
  margin-top: 4px !important;
}
#SignupIs {
    background: #fff;
    border-right: 10px solid #f0f0f0;
    padding: 35px 40px 35px;
    border-radius: 20px;
}
#SignupIs p.Mui-error {
    position: absolute;
    bottom: -18px;
    width: 100%;
}
#hideSlider {
  display: flex;
    background: #fff;
    border-radius: 20px;
    padding-right: 18px;
}

#loginFormIs{
    padding: 35px 40px 12px;
    border-right: 10px solid #f0f0f0;
    border-radius: 20px;
    min-height: 500px;
}
 #loginFormIs:has(#sendEmailButton), #resetPasswordPanel {
  min-height: 525px !important;
}
#loginFormIs:has(#resetButton), #emailVerificationSection > div[class*="formcontainer"]{
  min-height: 530px !important;
}
 /* #resetPasswordPanel { getting issue for set New password
  min-height: 494px !important;
} */
#hideSlider{
    padding-bottom: 14px;
}
#loginPanel{
  align-items: end;
}
 #loginPanelIs{
  margin-top: 5px;
 }
 .RightCore{
  overflow-y: auto;
}
.IntroContain{
  width: auto;
  background-size: 100% auto;
  background-repeat: no-repeat;
}
button#optionsBtnSAT span:nth-child(2) {
    text-transform: none !important;
    text-align: start !important;
}
#commonShadow{
  box-shadow: 0px 3px 3px -2px rgba(145, 158, 171, 0.2), 0px 3px 4px 0px rgba(145, 158, 171, 0.14), 0px 1px 8px 0px rgba(145, 158, 171, 0.12);
  background: #fff;
  border-radius: 15px;
  padding: 20px 15px;
  margin-bottom: 30px;
  overflow-x: auto;
}
[data-mode*="formula"],[class*="ql-tooltip"]{
  z-index: 99;
}
    #loginFormIs p.Mui-error {
      position: absolute !important;
      bottom: -20px !important;
      width: 100%;
    }
    #loginFormIs {
    display: flex;
    align-items: center;
    }
    nav[aria-label="breadcrumb"] ol li a,
    nav[aria-label="breadcrumb"] ol li button,
    nav[aria-label="breadcrumb"] ol li p  {
      color: #0000ee !important;
      font-size: 16px;
  }
  #emailVerificationSection > div:first-child{
    min-height: 533px !important;
  }
  #hideSliderBoxs [class*="carousel"]{
    max-height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
  }
  #hideSliderBoxs .carousel .slide img{
    max-height: 465px !important;
  }
    
@media screen and (max-width: 1536px) {
    #DescriptionPara p{
      max-width: 160px !important;
    }
}
@media screen and (min-width: 1535px) {
    [class*=maxWidthXl]:not(:has(>div>div.preventZoom)) {
        max-width: 100% !important;
    }
    #loginPanelIs {
      padding: 0;
    }
    #loginPanel {
      transform: scale(1.15);
    }
    #loginPanelIs {
      transform: scale(1.1);
    }
  }
 @media screen and (min-width: 1600px){
  #myMenu {
    max-height: Calc(100vh - 580px) !important;
    overflow-y: auto;
    min-height: 500px !important;
}
#simulationSec [class*="findBody2"]{
    max-height: 500px !important;
    background-size: 100% 500px !important;
}
[class*="Indigo"] img{
  max-height: 500px !important;
}
#sidebarPanelAside nav{
      max-height: calc(100vh - 780px) !important;
          overflow-y: auto;
          /* min-height: 415px !important; */
          min-height: fit-content !important;
}
 }
 #sidePanelAsideB2B{
  margin-top: 50px;
  padding-top: 12px;
  height: Calc(100vh - 70px);
  overflow-y: auto;
 }

[class*="editor-element"] span[class*="latex"] ul li span{
  /* display: block !important; */
  text-align: inherit !important;
}


    @media screen and (min-width: 1400px){
        [class*="SignupPanel"]{
            min-height: '100%';
            display: 'flex';
            align-items: 'center';
        }
    }
    @media screen and (min-width: 1650px) {
        #loginPanel {
          transform: scale(1.2);
        }
        #hideSlider, #loginFormIs{
          flex: 1;
          display: flex;
        }
        #loginFormIs img {
        width: 100%;
        height: 100%;
        object-fit: cover;  
      }
   
        #loginPanelIs {
          transform: scale(1.2);
        }
        aside+div #simulationSec > div:nth-child(2){
          min-height: 515px !important;
          background-size: 100% 500px;
        } 
    }

      @media screen and (max-width: 900px){
          #hideSlider {
          display: none !important;
        }
        #loginFormIs{
          height: 90vh;
          /* padding-top: 75px; */
          padding-top: 50px;
        }
        #SignupIs{
          height: 90vh;
          padding-top: 30px;
        }
          #loginPanelIs{
          margin-left: 10px !important;
        }
      }
      @media screen and (max-width: 768px){

  #sidePanelAsideB2B {
    margin-top: 0px;

  }
  #mobileSidebar{
    display: flex !important;
    flex-direction: column;
  }
  #nestedListSubHeading{
    font-size: 14px;
  }
  #mobileSidebarItem {
    margin-bottom: 5px;
    padding: 5px 15px;
  }
  #toggleButtonGroupSwitch{
    padding: 5px !important;
    font-size: 12px !important;
  }
  header ul{
    display: none;
  }

 }
@media screen and (max-width: 768px) {
  #profileContainer {
    margin-left: 0 !important;
  }  
  .cardShowEven {
    align-items: start !important;
  }
   #topicHead + #QuestionPanel, #topicHead + div {
        max-height: Calc(100vh - 270px) !important;
        overflow: auto !important;
    }
  .MuiToolbar-root {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
  #userInfoName{
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .MuiToggleButtonGroup-root {
    margin-right: 10px !important;
  }
  #sidebarPanelAside  audio{
    width: revert !important;
  }
    #GridContainer {
    flex-direction: column-reverse !important;
  }
  [data-testid="ExpandMoreIcon"]{
    width: unset !important;
  }
   h6 > .subModuleName + span {
    margin-bottom: 5px !important;
   }
   #simulationSec div:has(video) {
    height: auto !important;
   }
   #SidebarQuestionPanel{
    padding-top: 0px !important;
   }
   #GridContainer #sidebarPanelAside{
    order: 1 !important;
   }
   #GridContainer #SidebarQuestionPanel{
    order: 2 !important;
   }
   #GridContainer .preventZoom {
   order: 3 !important;
   }
}

@media screen and (max-width: 600px) {

  .MuiToggleButtonGroup-root .MuiToggleButton-root {
    padding: 6px 8px !important;
    font-size: 0.8rem !important;
  }
  
  .MuiToolbar-root {
    min-height: 64px !important;
    flex-wrap: nowrap !important;
  }
  #fontSmall{
    font-size: 12px !important;
  }
}


@media screen and (max-width: 480px) {
  .MuiToggleButtonGroup-root {
    transform: scale(0.75) !important;
    transform-origin: right center !important;
  }
  #NEETAnalyticsAttemps{
    flex-direction: column-reverse !important;
  }

        #loginPanel {
         margin-bottom: 70px !important;
        }
        #loginFormIs
         /* #SignupIs */
         {
            height: auto !important;
        }
      #greetingCard {
            flex-direction: column !important;
            align-items: start !important;
      }
     .RightCore [class*="CardContent"] div:not(:first-child){
           align-items: center !important;
      }
      [class*="practiceTimeInstructions"] [class*="CardContent"] div:not(:first-child){
            flex-direction: row !important;
            align-items: center !important;
      }
          nav[aria-label="breadcrumb"] ol li a,
    nav[aria-label="breadcrumb"] ol li button,
    nav[aria-label="breadcrumb"] ol li p  {
      font-size: 15px;
  }
  /* .MuiToolbar-root img {          keyskillset logo was very small in mobile screen, so commented
    max-height: 35px !important;
  } */
}
@media screen and (max-height: 760px){
  #GridContainer:has(video){
       transform: scaleY(0.95);
       top: -16px;
    }
}

