/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import {
    Grid, Button, IconButton, TextField,
    InputAdornment, Table, TableBody, TableCell, TableContainer,FormControlLabel, Checkbox,
    TableHead, TableRow, Paper, TablePagination, Switch,Tooltip ,DialogActions,DialogContent,DialogTitle,Dialog 
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import moment from 'moment'
import { useNavigate } from 'react-router-dom';
import EditIcon from '@mui/icons-material/Edit';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DeleteAlert from '../../../components/modal/DeleteModal';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';

const Sat = () => {
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [searchedDetails, setSearchedDetails] = useState('');
    const [satDetails, setSatDetails] = useState([]);
    const [deleteAlert, setDeleteAlert] = useState(false);
    const [deleteModalTitle, setDeleteModalTitle] = useState('');
    const [deleteId, setDeleteId] = useState('')
    const [deleteType, setDeleteType] = useState('')
    const [totalCount, setTotalCount] = useState('')
    const [openUserMessage, setOpenUserMessage] = useState(false)
    const [userMessageData, setUserMessageData] = useState('')
    const [userMessageFromDateAndTime, setUserMessageFromDateAndTime] = useState('')
    const [userMessageToDateAndTime, setUserMessageToDateAndTime] = useState('')
    const [currentToggleId, setCurrentToggleId] = useState('')
    const [currentToggleValue, setCurrentToggleValue] = useState('')
    const [selected, setSelected] = useState({});

    const { t } = useTranslation('translation');
    const navigate = useNavigate();

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };
    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    useEffect(() => {
        getSATDetails()
    }, [page, rowsPerPage, searchedDetails])


    const getSATDetails = async () => {
        const result = await adminServices.getSATAssessmentAdmin(searchedDetails, page, rowsPerPage,);
        if (result.ok) {
            setSatDetails(result.data.assessments)
            setTotalCount(result.data.pagination)
        }

    }


const callUpdateStatusAPI = async (id, value, userData) => {    
  try {
    const result = await adminServices.UpdateGeneralAssessmentStatus(id, value, userData,'Status');
    if (result.ok) {
      setSnackbarTitle(t('SAT Assessment status updated successfully'));
      setOpenSnackbar(true);
      getSATDetails();
    }
  } catch (error) {
    console.log(error, 'error');
  }
};

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const handleSubmitUserMessage = () => {
        const userData = {
            message: userMessageData,
            fromDate: formatDate(userMessageFromDateAndTime),
            toDate: formatDate(userMessageToDateAndTime),
        };        
        callUpdateStatusAPI(currentToggleId, currentToggleValue, userData);
        setOpenUserMessage(false);
        setUserMessageData('');
        setUserMessageFromDateAndTime('');
        setUserMessageToDateAndTime('');
    };





    const handleCreateSAT = () => {
        // setOpenCreateSat(true)
        navigate("/app/satassessment")
    }

    const handleDelete = (data) => {
        setDeleteAlert(true);
        setDeleteId(data.id);
        setDeleteType(data?.question_type);
        setDeleteModalTitle('Are you sure you want delete this Sat Question?');
    }

    const handleIRT = async (data) => {

        const res = await adminServices.IRTSatAssessment(data.id)
        console.log(res, "Irt hitting")
    }

    const handleEditAssessment = (data) => {
        navigate('/app/editSatassessment', { state: data?.assessment?.id })
    }

    const handleDeleteSat = async () => {
        try {
            const response = await adminServices.deleteNEETAssessment(deleteId);
            if (response.ok) {
                setSnackbarTitle('Sat  deleted successfully');
                setOpenSnackbar(true);
                getSATDetails();
                setDeleteAlert(false)
                setDeleteId('')
                setDeleteType('')
                setDeleteModalTitle("");
            }
        } catch (error) {
            console.log(error);
        }
    }
    const handleChangeCheckBox = (rowId, name) => {
        const current = selected[rowId];
        // if (current && current !== name) {
        //     alert("Please unselect the current checkbox before selecting another.")
        //     return;
        // }

        const newValue = current === name ? null : name;

        setSelected((prev) => ({
            ...prev,
            [rowId]: newValue,
        }));

         if(name === "ON_MAINTENANCE"){
            setOpenUserMessage(true);
            setCurrentToggleId(rowId);
            setCurrentToggleValue(newValue);
        }

        callUpdateStatusAPI(rowId, newValue);
    };



    return (
        <Page title="Sat Assessment">
            <PageHeader pageTitle="Sat Assessment" submodule="submodule" />
            <>
                <Grid container spacing={2}>
                    <Grid align="end" item xs={12}>
                        <TextField
                            variant="outlined"
                            placeholder="Search Name"
                            id="searchassessment"
                            size="small"
                            sx={{ width: '250px', height: '40px' }}
                            value={searchedDetails}
                            onChange={(e) => setSearchedDetails(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon id="searchAssessmentIconbtn" />
                                    </InputAdornment>
                                ),
                                sx: { height: '100%' },
                            }}
                        />
                        <Button
                            style={{ marginLeft: '15px', height: '40px' }}
                            id="createassessmentButtongeneral"
                            variant="contained"
                            color="primary"
                            onClick={handleCreateSAT}
                        >
                            Create SAT
                        </Button>
                    </Grid>
                    <TableContainer component={Paper} sx={{ marginTop: 2 }}>
                        <Table sx={{ marginLeft: '15px', width: '98%' }}>
                            <TableHead>
                                <TableRow>
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', paddingLeft: '8px !important' }}>Name</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Name</TableCell>

                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Live</TableCell> */}
                                    {/* <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Questions</TableCell> */}
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Level</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Status</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Author</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Date</TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {satDetails && satDetails?.length > 0 && (
                                    satDetails?.map((row) => (
                                        <TableRow key={row?.assessment?.id} sx={{ '&:hover': { backgroundColor: '#f1f1f1' } }}>
                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.assessment?.title}</TableCell>
                                            <TableCell sx={{ marginLeft: '40px' }}>{row?.assessment?.complexity_level.charAt(0).toUpperCase() + row?.assessment?.complexity_level.slice(1).toLowerCase()}</TableCell>
                                          
                                              <div style={{ display: "flex", flexDirection: "row", gap: "8px",padding:'16px' }}>
                                                                                                <FormControlLabel
                                                                                                    control={
                                                                                                        <Checkbox
                                                                                                            checked={
                                                                                                                selected[row.assessment.id]
                                                                                                                    ? selected[row.assessment.id] === "ACTIVE"
                                                                                                                    : row.assessment.status_type === 1
                                                                                                            }
                                                                                                            onChange={() => handleChangeCheckBox(row.assessment.id, "ACTIVE")}
                                                                                                        />
                                                                                                    }
                                                                                                    label="Active"
                                                                                                />
                                                                                                <FormControlLabel
                                                                                                    control={
                                                                                                        <Checkbox
                                                                                                            checked={
                                                                                                                selected[row.assessment.id]
                                                                                                                    ? selected[row.assessment.id] === "INACTIVE"
                                                                                                                    : row.assessment.status_type === 2
                                                                                                            }
                                                                                                            onChange={() => handleChangeCheckBox(row.assessment.id, "INACTIVE")}
                                                                                                        />
                                                                                                    }
                                                                                                    label="InActive"
                                                                                                />
                                                                                                <FormControlLabel
                                                                                                    control={
                                                                                                        <Checkbox
                                                                                                            checked={
                                                                                                                selected[row.assessment.id]
                                                                                                                    ? selected[row.assessment.id] === "ON_MAINTENANCE"
                                                                                                                    : row.assessment.status_type === 4
                                                                                                            }
                                                                                                            onChange={() => handleChangeCheckBox(row.assessment.id, "ON_MAINTENANCE")}
                                                                                                        />
                                                                                                    }
                                                                                                    label="Maintenance"
                                                                                                />
                                                                                            </div>
                                            <TableCell>{row?.assessment?.author_first_name}</TableCell>

                                            <TableCell>
                                                {moment(row.assessment?.created_date_time).format("DD/MM/YYYY")}
                                            </TableCell>
                                            <TableCell sx={{ padding: '10px' }}>
                                                <IconButton
                                                     disabled={!(
                                                        selected[row.assessment.id]
                                                          ? ["INACTIVE", "ON_MAINTENANCE"].includes(selected[row.assessment.id])
                                                          : [2, 4].includes(row.assessment.status_type)
                                                      )}
                                                    // disabled={row?.assessment.is_published === true}
                                                    id={`editsat${row?.assessment?.id}`}
                                                    onClick={() => handleEditAssessment(row)}
                                                    color="primary">
                                                    <EditIcon />
                                                </IconButton>
                                                <IconButton
                                                     disabled={!(
                                                        selected[row.assessment.id]
                                                          ? ["INACTIVE", "ON_MAINTENANCE"].includes(selected[row.assessment.id])
                                                          : [2, 4].includes(row.assessment.status_type)
                                                      )}
                                                    // disabled={row?.assessment.is_published === true}
                                                    id={`deletesat${row?.assessment?.id}`}
                                                    onClick={() => handleDelete(row?.assessment)}
                                                    color="primary">
                                                    <DeleteIcon />
                                                </IconButton>

                                                <Tooltip title="Trigger IRT">
                                                    <IconButton
                                                        id={`Trigger${row?.assessment?.id}`}
                                                        onClick={() => handleIRT(row?.assessment)}
                                                        color="primary"
                                                    >
                                                        <BookmarkIcon />
                                                    </IconButton>
                                                </Tooltip>

                                            </TableCell>
                                        </TableRow>
                                    ))
                                )
                                  
                                }
                            </TableBody>




                        </Table>
                    </TableContainer>

                    <TablePagination
                        component="div"
                        count={totalCount?.total_count}
                        page={page}
                        onPageChange={handleChangePage}
                        rowsPerPage={rowsPerPage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        rowsPerPageOptions={[5, 10, 15, 20, 25]}
                        sx={{ marginTop: 2 }}
                    />
                    <DeleteAlert
                        open={deleteAlert}
                        title={deleteModalTitle}
                        confirm={handleDeleteSat}
                        close={() => setDeleteAlert(false)}
                    />
                    <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
                    {/* <CreateSATQuestionModel
                        open={openCreateSat}
                        modelClose={() => setOpenCreateSat(!openCreateSat)}
                        title="Create Sat"
                        // handleCreateMcq={handleSubmitForm}
                        loading={loading}
                        // searchedDetails={searchedDetails}
                        CallBack={CallBackRemove}
                    /> */}
                </Grid>
            </>
            <Dialog  open={openUserMessage} onClose={() => setOpenUserMessage(false)}>
  <DialogTitle>Provide Message and Date Range</DialogTitle>
  <DialogContent>
    <TextField
      label="Message"
      value={userMessageData}
      onChange={(e) => setUserMessageData(e.target.value)}
      fullWidth
      multiline
      rows={3}
      margin="normal"
    />
    <TextField
      label="From Date"
      type="datetime-local"
      value={userMessageFromDateAndTime}
      onChange={(e) => setUserMessageFromDateAndTime(e.target.value)}
      fullWidth
      margin="normal"
      InputLabelProps={{ shrink: true }}
    />
    <TextField
      label="To Date"
      type="datetime-local"
      value={userMessageToDateAndTime}
      onChange={(e) => setUserMessageToDateAndTime(e.target.value)}
      fullWidth
      margin="normal"
      InputLabelProps={{ shrink: true }}
    />
  </DialogContent>
  <DialogActions>
    <Button onClick={() => setOpenUserMessage(false)}>Cancel</Button>
    <Button onClick={handleSubmitUserMessage} variant="contained" color="primary">
      Submit
    </Button>
  </DialogActions>
</Dialog >

        </Page>
    )
}

export default Sat;