/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-danger */
/* eslint-disable jsx-a11y/role-has-required-aria-props */
/* eslint-disable consistent-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable arrow-body-style */
/* eslint-disable func-names */
/* eslint-disable no-alert */

import React, { useState, useEffect, useRef } from 'react';
import MenuItem from '@mui/material/MenuItem';
import {
    TextField, Button, Box, Badge, Avatar, Typography, IconButton, FormHelperText, InputLabel, Select, FormControl, Grid, Dialog,
    DialogActions, DialogContent, Tooltip,
    Checkbox, CardContent, CircularProgress,
    DialogTitle, Alert, Radio, RadioGroup, FormControlLabel, FormGroup, CardActionArea, Card, Stack
} from "@mui/material";
import { v4 as uuidv4 } from 'uuid';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { LoadingButton } from '@mui/lab';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom'
import { DropzoneArea } from 'material-ui-dropzone';
import DOMPurify from 'dompurify';
import { makeStyles } from '@mui/styles';
import katex from "katex";
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DialogModal from '../../../components/modal/DialogModal';
import './styles.css'
import "katex/dist/katex.min.css";
import QuillEditor from '../../../components/Quill/Quill'

window.katex = katex;


const NEET = () => {
    const userInfo = useSelector((state) => state.userInfo && state.userInfo);
    const navigate = useNavigate();
    const classes = useStyles();
    const [loading, setLoading] = useState(false);
    const [questionList, setQuestionList] = useState([]);
    const [category, setCategory] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [name, setName] = useState('');
    const [questionid, setQuestionid] = useState([]);
    const [submitQuestionClicked, setSubmitQuestionClicked] = useState(false);
    const [questionDetails, setQuestionDetails] = useState('');
    const [nameError, setNameError] = useState('');
    const [ImageError, setImageError] = useState('');
    const [descriptionError, setDescriptionError] = useState('');
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    const [loadingQuestion, setLoadingQuestion] = useState(false);
    const [search, setSearch] = useState('');
    const [page, setPage] = useState(0);

    // eslint-disable-next-line no-unused-vars
    const [error, setError] = useState(false);
    const [requirederrors, setrequiredErrors] = useState({
        moduleName: "",
        points: "",
        questionid: "",
    });
    const [nodata, setNodata] = useState(true);
    const [thumbImage, setThumbImage] = useState(null);
    const [thumbPreview, setThumbPreview] = useState(null);
    const [editorValue, setEditorValue] = useState('');
    // const [neetType,setNeetType] = useState('');
    // const [neetTypeError,setNeetTypeError] = useState('');
    const [openDialog, setOpenDialog] = useState(false);
    const [OpenDialogNew, setOpenDialogNew] = useState(false);
    const [visible, setVisible] = useState(true);
    const [editData, setEditData] = useState("");
    const [editIndex, setEditIndex] = useState("");
    const [editIndexnew, setEditIndexnew] = useState("");
    const [editDialog, setEditDialog] = useState(false);
    const [query, setQuery] = useState('');
    const [moduleName, setModuleName] = useState('');
    const [modulesArray, setModulesArray] = useState([]);
    const quillRefs = {
        question: React.useRef(null),
        explanation: React.useRef(null),
        mcqOptions: React.useRef([])
    };



    const [currentModule, setCurrentModule] = useState({
        name: '',
        questions: []
    });
    const [currentTopic, setCurrentTopic] = useState({
        name: '',
        questions: []
    });
    const [openModuleDialog, setOpenModuleDialog] = useState(false);
    const [openTopicDialog, setOpenTopicDialog] = useState(false);
    const [currentModuleIndex, setCurrentModuleIndex] = useState(null);
    const [currentTopicIndex, setCurrentTopicIndex] = useState(0);
    console.log(currentTopicIndex, "currentTopicIndex");

    const [selectIndex, setSelectIndex] = useState('');
    const [explanation, satExplanation] = useState('');
    const [selectedOptionnew, setSelectedOptionnew] = useState(null);
    const [points, setPoints] = useState('');
    const [satValues, setSatValues] = useState({
        question: '',
        questionType: 'Chemistry',
        mcqOptions: [{ option: '', isCorrect: false }],
    });

    const [questionError, setQuestionError] = useState({
        passage: "",
        question: "",
        option: "",
        Objective: '',
        explanation: ''
    });
    const [open, setOpen] = useState(false);
    const [moduleData, setModuleData] = useState([]);
    const [errorMessage, setErrorMessage] = useState("");
    const [errorMessageAll, setErrorMessageAll] = useState("");
    const [selectedOption, setSelectedOption] = useState('create');
    const [selectedSkills, setSelectedSkills] = useState('');
    const [loadingnew, setLoadingnew] = useState(false);
    const [submitted, setSubmitted] = useState(false);
    const [Preview, setPreview] = useState(false);
    const [previewData, setPreviewData] = useState('');
    const [details, setDetails] = useState({ level: "", questionType: "Chemistry", studyMaterials: [] });




    const handleUploadImage = async (file) => {
        const formData = new FormData();
        formData.append("thumbImage", file);
        try {
            const imageUrl = await adminServices.postImag(formData);
            if (imageUrl?.ok) {
                console.log(imageUrl.data, "222222222222222");

                return imageUrl.data;
            }
        } catch (error) {
            console.error("Image upload failed", error);
        }
        return null;
    };

    const imageHandler = () => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/*";
        input.click();

        input.onchange = () => {
            if (input.files?.length) {
                handleFiles(input.files);
            }
        };
    };


    const handleFiles = async (files, targetRef) => {
        const quillInstance = targetRef?.current?.getEditor();
        if (!quillInstance) return;

        for (const file of files) {
            if (file.type.startsWith("image/")) {
                const range = quillInstance.getSelection(true);
                const tempId = `temp-${Date.now()}`;
                quillInstance.insertEmbed(range.index, "image", `/loading.gif?${tempId}`);
                quillInstance.setSelection(range.index + 1);

                const url = await handleUploadImage(file);
                if (url) {
                    const editorEl = quillInstance.root;
                    const imgEl = editorEl?.querySelector(`img[src="/loading.gif?${tempId}"]`);
                    if (imgEl) imgEl.setAttribute("src", url);
                }
            }
        }
    };

    React.useEffect(() => {
        const mountedRefs = Object.values(quillRefs).filter(ref => ref?.current);
        if (!mountedRefs.length) return;

        mountedRefs.forEach((ref) => {
            const editor = ref.current.root;
            if (!editor) return;

            const pasteHandler = (e) => {
                const items = e.clipboardData?.items;
                if (items) {
                    const files = [];
                    for (const item of items) {
                        if (item.type.indexOf("image") !== -1) {
                            e.preventDefault();
                            files.push(item.getAsFile());
                        }
                    }
                    if (files.length) handleFiles(files, ref);
                }
            };

            const dropHandler = (e) => {
                e.preventDefault();
                const files = e.dataTransfer?.files;
                if (files?.length) handleFiles(files, ref);
            };

            editor.addEventListener("paste", pasteHandler);
            editor.addEventListener("drop", dropHandler);
            editor.addEventListener("dragover", (e) => e.preventDefault());

            return () => {
                editor.removeEventListener("paste", pasteHandler);
                editor.removeEventListener("drop", dropHandler);
            };
        });
    }, [quillRefs.question.current, quillRefs.explanation.current]);


    const ClearError = () => {
        setrequiredErrors({
            moduleName: "",
            points: "",
            questionid: "",
        });
    }




    const handleChangeCheck = (event) => {
        setQuestionError(
            {
                Objective: ''
            }
        )
        setSelectedSkills(event.target.value);
    };



    const handleChangeDescription = (value) => {
        const cleanedValue = value
            .replace(/<p><br><\/p>/g, '')
            .replace(/<p><\/p>/g, '')
            .trim();
        setDescriptionError("");
        setEditorValue(cleanedValue);
    };

    const handleLeftArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev - 1);
        getQuestionList(details.questionType, search)
    }
    const handleRightArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev + 1);
        getQuestionList(details.questionType, search)
    }

    const handlePreview = () => {
        setPreview(false)
        setEditIndexnew('')
        setPreviewData('')
    }


    const handlePreviewOpen = async (data, index) => {
        setEditIndexnew(index)
        setPreview(true)
        if (data && data?.questions && data?.questions?.length > 0) {
            const val = await adminServices.getMultipleQuestionDetails(data.questions);
            data.questiondetails = data?.questions?.map(id => {
                return val.data.find(question => question.id === id);
            });
        }
        setPreviewData(data)
    }

    const DeleteQuestion = (id, index) => {
        setPreviewData((prev) => ({
            ...prev,
            questions_list: prev?.questions.filter((_, i) => i !== index),
            questiondetails: prev?.questiondetails.filter((_, i) => i !== index),
            questions: prev?.questions.filter((_, i) => i !== index),
        }));
        setModulesArray((prevModules) =>
            prevModules.map((module) => ({
                ...module,
                questions: module.questions.filter((qid) => qid !== id),
                // questiondetails: module.questiondetails.filter((qd) => qd.id !== id),
            }))
        );
    };

    const handleUpdateQuestions = (editIndexnew) => {
        setModuleData((prevModuleData) =>
            prevModuleData.map((item, index) =>
                index === editIndexnew ? { ...item, ...previewData } : item
            )
        );
        setPreview(false);
    };
    const handlePoints = (e) => {
        const value = e.target.value.replace(/\D/, '');
        if (value === '0') {
            setrequiredErrors({
                points: "Value cannot be 0",
            })
        } else {
            setrequiredErrors({
                points: "",
            })
        }
        setPoints(value);
    }

    useEffect(() => {
        setLoading(false);
        setLoadingQuestion(true)
        getQuestionList(details.questionType, search);
    }, [details.questionType, search]);




    const sanitizeConfig = {
        ALLOWED_TAGS: ['b', 'i', 'em', 'a', 'ul', 'ol', 'li'],
    };


    const getQuestionList = async (data, search) => {

        const result = await adminServices.getSatQuestion(data, search, page)
        setQuestionList(result.data)
        setLoadingQuestion(false)
        if (result.ok) {
            if (result.data?.length === 0) {
                setNodata(true)
            }
            else {
                setNodata(false)
            }
        }
    };



    const mcqOptionsRef = useRef(satValues.mcqOptions);

    useEffect(() => {
        mcqOptionsRef.current = satValues.mcqOptions;
    }, [satValues.mcqOptions]);




    const handleMCQOptionChangeEnglish = (index, field, value) => {
        // const newMCQOptions = [...satValues.mcqOptions];
        const newMCQOptions = [...mcqOptionsRef.current];

        newMCQOptions[index][field] = value;
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };



    const handleRemoveOptionEnglish = (index) => {
        const newMCQOptions = [...satValues.mcqOptions];
        newMCQOptions.splice(index, 1);
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };



    const handleFileChange = (file) => {
        setImageError("")
        if (file[0]?.size < 2097152) {
            imageWidthAndHeight(file[0]).then((res) => {
                if (res.width >= 360 && res.height >= 200) {
                    const url = URL.createObjectURL(file[0]);
                    setThumbPreview(url);
                    setThumbImage(file[0]);
                } else {
                    alert("Image dimensions must be at least 360x200px.");
                }
            });
        }
    };

    const imageWidthAndHeight = (file) => {
        return new Promise((resolve) => {
            const img = new Image();
            const reader = new FileReader();

            reader.onload = function () {
                img.onload = function () {
                    resolve({ width: img.width, height: img.height });
                };
                img.src = reader.result;
            };
            reader.readAsDataURL(file);
        });
    };



    const handleChangeQuestionEnglish = (field, value) => {
        setQuestionError({
            Objective: ''
        });
        setSatValues((prevState) => ({
            ...prevState,
            [field]: value,
        }));
    };

    const handleAddOptionEnglish = () => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: [...prevState.mcqOptions, newOption],
        }));
    };


    const Validation = () => {
        const isEditorContentEmpty = (html) => {
            const text = html?.replace(/<(.|\n)*?>/g, '').trim();
            return !text;
        };
        if (!name) {
            setNameError("Name field is required");
            return false;
        }
        // if (!neetType) {
        //     setNeetTypeError("Type field is required");
        //     return false;
        // }

        if (isEditorContentEmpty(editorValue)) {
            setDescriptionError("Description field is required");
            return false;
        }

        if (!thumbImage) {
            setImageError("ThumbImage field is required");
            return false;
        }


        if (editorValue?.length > 255) {
            setDescriptionError("Description cannot be more than 255 characters");
            return false;
        }

        if (!modulesArray || modulesArray.length === 0) {
            setErrorMessage("Please add at least one module.");
            return false;
        }


        return true;
    };

    const handleSubmit = async () => {
        const valid = Validation()


        if (valid) {
            setLoading(true);

            // eslint-disable-next-line no-unused-vars
            const totalSeconds = (time.hours * 3600) + (time.minutes * 60) + time.seconds;
            const selectedQuestions = modulesArray && modulesArray.flatMap(item => item.questions);
            const formData = new FormData();
            formData.append('name', name);
            formData.append('description', editorValue);
            // formData.append('neetType', neetType);
            formData.append('time_in_mins', totalSeconds);
            // formData.append('time_in_mins', 7920);
            formData.append('thumbImage', thumbImage);
            formData.append('modules', JSON.stringify(modulesArray));
            formData.append('is_published', false);
            formData.append('userId', userInfo.id);
            formData.append('selectedQuestions', selectedQuestions);
            formData.append('is_free', false);

            try {
                const response = await adminServices.NEETCreation(formData);
                if (response.ok) {
                    setSnackbarTitle('NEET created successfully');
                    setOpenSnackbar(true);
                    navigate("/app/NEET");
                    CloseFunction()
                    setModuleData([])
                    setName('')
                    setLoading(false);
                }

            } catch (error) {
                console.log(error);
            }

        }
    };

    const CloseFunction = () => {
        setCategory("")
        setName("")
        setEditorValue("")
        // setQuestionList([])
        setNameError("")
        setImageError("")
        setDescriptionError("")
        // setNeetTypeError('')
        setThumbPreview(null)
        setThumbImage(null)
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''


        });
        setrequiredErrors({
            questionid: "",
        })
    }

    const [time, setTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0,
    });

    const handleChange = (event) => {
        const { name, value } = event.target;
        setTime((prevTime) => ({
            ...prevTime,
            [name]: value,
        }));
    };

    const hoursArray = Array.from({ length: 24 }, (_, i) => i);
    const minutesArray = Array.from({ length: 60 }, (_, i) => i);
    const secondsArray = Array.from({ length: 60 }, (_, i) => i);



    const handleCloseNew = () => {
        setModuleName('');
        setPage(0);
        setPoints("")
        setSubmitted(false);
        setQuestionid('')
        setSelectedQuestions([])
        setQuestionDetails('')
        setVisible(true);
        setSelectedOption('create')
        setDetails({ level: "", questionType: "Chemistry", studyMaterials: [] });
        setSelectedSkills("")

        satExplanation('');
        setSatValues({});
        setTimeout(() => {

            setSatValues({
                question: '',
                questionType: 'Chemistry',
                mcqOptions: [{ option: '', isCorrect: false }],
            });

        }, 0);
        setSelectedSkills("");
        setOpenDialog(false);
        setOpenDialogNew(false);
        setrequiredErrors({
            questionid: "",
        })
    };

    const CreateQuestion = (e) => {
        setSearch(e.target.value)
        const newId = e.target.value.id;
        setQuestionid((prev) => [...prev, newId]);
        setVisible(true);
    }


    const stripHtml = (html) => {
        const div = document.createElement("div");
        div.innerHTML = html;
        return div.textContent || div.innerText || "";
    };

    // const isEmptyHtmlContent = (html) => {
    //     return stripHtml(html).trim() === "";
    // };

    const isEmptyHtmlContent = (htmlString) => {
        if (!htmlString) {
            return true;
        }
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlString;

        if (tempDiv.querySelector('img') !== null) {
            return false;
        }

        if (tempDiv.querySelector('.ql-formula') !== null) {
            return false;
        }

        const cleanText = stripHtml(htmlString)
            .replace(/[\u200B-\u200D\uFEFF]/g, '')
            .trim();

        if (cleanText === '') {
            const normalizedHtml = htmlString.replace(/<p><br><\/p>/g, '')
                .replace(/<br>/g, '')
                .trim();
            return normalizedHtml === '';
        }
        return false;
    };

    const handleQuestionSubmit = () => {

        if (details?.questionType === 'Chemistry' || details?.questionType === 'Physics' || details?.questionType === 'Biology') {
            if (

                !selectedSkills && !explanation &&
                (!satValues?.question?.trim() || !satValues?.mcqOptions?.length ||
                    satValues.mcqOptions.some(option => !option.option.trim()) ||
                    !satValues.mcqOptions.some(option => option.isCorrect === true))) {
                setQuestionError({
                    question: "This field is required",
                    Objective: "Please Select Level of Objective",
                    explanation: "This field is required"
                });
                return false;
            }
            if (!satValues?.question || isEmptyHtmlContent(satValues.question)) {
                setQuestionError({ question: "This field is required" });
                return false;
            }

            if (!selectedSkills) {
                setQuestionError({ Objective: "Please Select Level of Objective" });
                return false;
            }

            if (
                !satValues?.mcqOptions?.length ||
                satValues.mcqOptions.some(option => isEmptyHtmlContent(option.option))
            ) {
                setQuestionError({ option: "Each option must be filled" });
                return false;
            }

            if (satValues.mcqOptions.some(option => option.option.trim()) && !satValues.mcqOptions.some(option => option.isCorrect === true)) {
                setQuestionError({ correctAnswer: "At least one option must be selected as correct" });
                return false;
            }
            if (satValues.mcqOptions?.length < 4) {
                setQuestionError({ correctAnswer: "At least 4 options to be selected " });
                return false;
            }
            if (!explanation) {
                setQuestionError({ explanation: "This field is required" });
                return false;
            }
            return true;
        }


    };






    const handleModuleSubmitCreateQuestion = (selectIndex) => {
        if (questionid && selectedSkills && submitQuestionClicked) {
            setSubmitQuestionClicked(false);
            const updatedModuleData = moduleData.map((module, index) => {
                if (index === selectIndex) {
                    return {
                        ...module,


                        questions_list: Array.from(new Set([...module.questions_list, ...selectedQuestions.map(item => item.id)])),
                        questiondetails: [...module.questiondetails, ...selectedQuestions.map(item => item.question_text)],
                        // questions_list: Array.from(new Set([...module.questions_list, ...questionid])),
                        // questiondetails: [...module.questiondetails, questionDetails],

                    };
                }
                return module;
            });

            setModuleData(updatedModuleData);
            setModuleName('');
            setPoints('');
            setSubmitted(false);
            setQuestionid('');
            setVisible(true);
            setQuestionDetails('')
            setSelectedOption('create')
            setSelectedQuestions([])

            satExplanation('');
            setSatValues({})
            setTimeout(() => {


                setSatValues({
                    question: '',
                    questionType: 'Chemistry',
                    mcqOptions: [{ option: '', isCorrect: false }],
                });

            }, 0);

            setSelectedSkills('');
            handleCloseNew();

        }
        else {
            setrequiredErrors({
                questionid: "Please Submit question and Answer!",
            })

            if (!selectedSkills) {
                setQuestionError({ Objective: "Please Select Level of Objective" });
            }

        }
    };

    const handleAddQuestion = (question) => {
        setSelectedQuestions((prev) => {
            if (prev.find(q => q.id === question.id)) return prev;

            return [...prev, question];
        });
        setSelectedSkills(question?.name ? question?.name : '')
        setQuestionid((prev) => [...prev, question.id]);
    };
    let buttonText = "Submit Question";
    if (loadingnew) {
        buttonText = "Submitting...";
    } else if (submitted) {
        buttonText = "Submitted";
    }

    const handleChangeOption = (e) => {
        setSelectedOption(e.target.value)
        if (e.target.value === 'select') {
            setSubmitQuestionClicked(true);
        }

        setrequiredErrors({
            moduleName: "",
            points: "",
            questionid: "",
        });
    }


    const handleDeselect = (id) => {
        setSelectedQuestions((prev) => prev.filter((q) => q.id !== id));
        // setSelectedSkills(question?.name?question?.name:'')
        setQuestionid((prev) => prev.filter((q) => q !== id));
    };

    function sanitizeMathsQuestion(question) {
        if (!question) return '';
        return question
            .replace(/<p>/g, '<span style="display: flex;">')
            .replace(/<\/p>/g, '</span>')
            .replace(/&nbsp;/g, ' ');
    }

    // Topic handlers
    const addTopic = () => {
        setCurrentModule(prev => ({
            ...prev,
            topics: [...prev.topics, {
                name: '',
                questions: []
            }]
        }));
    };

    const removeTopic = (topicIndex) => {
        setCurrentModule(prev => ({
            ...prev,
            topics: prev.topics.filter((_, idx) => idx !== topicIndex)
        }));
    };

    const handleTopicChange = (topicIndex, field, value) => {
        setCurrentModule(prev => {
            const newTopics = [...prev.topics];
            newTopics[topicIndex] = { ...newTopics[topicIndex], [field]: value };
            return { ...prev, topics: newTopics };
        });
    };

    // Question handlers
    const addQuestionToTopic = (topicIndex) => {
        setOpenDialog(true);
        setCurrentTopicIndex(topicIndex);
    };

    const addQuestionToModule = (question) => {
        setCurrentModule(prev => {
            const newTopics = [...prev.topics];
            newTopics[currentTopicIndex].questions.push(question);
            return { ...prev, topics: newTopics };
        });
    };

    const removeQuestion = (topicIndex, qIndex) => {
        setCurrentModule(prev => {
            const newTopics = [...prev.topics];
            newTopics[topicIndex].questions = newTopics[topicIndex].questions.filter((_, idx) => idx !== qIndex);
            return { ...prev, topics: newTopics };
        });
    };


    const handleModuleSubmit = () => {
        if (!currentModule.name.trim()) return;

        if (currentModuleIndex !== null) {
            // Update existing module
            const updatedModules = [...modulesArray];
            updatedModules[currentModuleIndex] = currentModule;
            setModulesArray(updatedModules);
        } else {
            // Add new module
            setModulesArray([...modulesArray, currentModule]);
        }

        setOpenModuleDialog(false);
    };

    const getModules = (targetRef) => ({
        toolbar: {
            container: [
                ["bold", "italic", "underline"],
                [{ list: "ordered" }, { list: "bullet" }],
                [{ script: "sub" }, { script: "super" }],
                [{ header: [1, 2, 3, false] }],
                ["image"],
                [{ color: [] }, { background: [] }],
                [{ align: [] }],
                ["clean"],
            ],
            handlers: {
                image: () => {
                    const input = document.createElement("input");
                    input.type = "file";
                    input.accept = "image/*";
                    input.click();

                    input.onchange = () => {
                        if (input.files?.length) {
                            handleFiles(input.files, targetRef);
                        }
                    };
                },
            },
        },
        clipboard: { matchVisual: false },
    });


    const questionModules = React.useMemo(() => getModules(quillRefs.question), []);
    const explanationModules = React.useMemo(() => getModules(quillRefs.explanation), []);


      const formats = React.useMemo(() => [
        "bold", "italic", "underline",
        "list", "bullet",
        "script",
        "header",
        "image",
        "color", "background",
        "align", "style","width", "height" 
      ], []); 


    const SubmitQuestion = async () => {
        const questionTest = handleQuestionSubmit();
        if (questionTest === true) {
            setLoadingnew(true);
            try {
                const keysData = new FormData();
                keysData.append('level', details.level);
                keysData.append('questionTag', JSON.stringify(details.studyMaterials || []));
                keysData.append('questionType', details.questionType);
                keysData.append('cognitive_skills', selectedSkills);

                const mcqdata = {
                    question: satValues.question,
                    mcqOptions: satValues.mcqOptions.map(option => option.option),
                    correctAnswer: satValues.mcqOptions.map(option => option.isCorrect),
                };
                keysData.append('question_text', satValues.question);
                keysData.append('mcqData', JSON.stringify(mcqdata));
                keysData.append('explanation', explanation);

                const response = await adminServices.createSatQuestion(keysData);
                if (response.ok) {
                    const updatedModules = [...modulesArray];
                    const currentModule = updatedModules[currentTopicIndex];
                    currentModule.questions.push(response?.data?.id?.id);
                    setSubmitted(false)
                    setModulesArray(updatedModules);
                    setSnackbarTitle('Question created successfully');
                    setOpenSnackbar(true);
                    setOpenDialog(false);
                    setLoadingnew(false);
                    setDetails({ level: "", questionType: "Chemistry", studyMaterials: [] });
                    satExplanation('');
                    setSatValues({
                        question: '',
                        mcqOptions: [{ option: '', isCorrect: false }]
                    });
                    setSelectedSkills('');
                }
                console.log.info("Question created successfully:", response.data);
            } catch (error) {
                console.error(error);
            } finally {
                setLoadingnew(false);
            }
        }
    }


    // // For selected questions
    // if (selectedOption === 'select') {
    //     // Add selected questions to the current topic
    //     const updatedModules = [...modulesArray];
    //     const currentTopic = updatedModules[currentModuleIndex].topics[currentTopicIndex];

    //     // Create a map of existing question IDs for quick lookup
    //     const existingQuestionIds = new Set(currentTopic.questions.map(q => q.id));

    //     selectedQuestions.forEach(question => {
    //         // Only add if not already present
    //         if (!existingQuestionIds.has(question.id)) {
    //             currentTopic.questions.push(question.id);
    //         }
    //     });

    //     setModulesArray(updatedModules);
    //     setOpenDialog(false);
    //     setSelectedQuestions([]);

    //     // Show success message
    //     setSnackbarTitle(`${selectedQuestions.length} question(s) added successfully`);
    //     setOpenSnackbar(true);
    // }


    const handleEditModule = (index) => {
        setCurrentModule(modulesArray[index]);
        setCurrentModuleIndex(index);
        setOpenModuleDialog(true);
    };

    const handleEditTopic = (index) => {
        setCurrentTopic(currentModule.topics[index]);
        setCurrentTopicIndex(index);
        setOpenTopicDialog(true);
    };


    const handleAddModule = () => {
        setCurrentModule({
            name: '',
            questions: []
        });
        setCurrentModuleIndex(null);
        setOpenModuleDialog(true);
    };




    const handleRemoveTopic = (index) => {
        const updatedModule = { ...currentModule };
        updatedModule.topics.splice(index, 1);
        setCurrentModule(updatedModule);
    };


    const [isEditingTopic, setIsEditingTopic] = useState(false);
    const [openQuestionDialog, setOpenQuestionDialog] = useState(false);

    // Handler functions
    const handleTopicSubmit = () => {
        if (currentTopic.name.trim() === '') {
            alert('Please enter a topic name');
            return;
        }

        const updatedModules = [...modulesArray];

        if (isEditingTopic) {
            // Update existing topic
            updatedModules[currentModuleIndex].topics[currentTopicIndex] = { ...currentTopic };
        } else {
            // Add new topic
            updatedModules[currentModuleIndex].topics.push({
                ...currentTopic,
                questions: currentTopic.questions || []
            });
        }

        setModulesArray(updatedModules);
        setOpenTopicDialog(false);
        setIsEditingTopic(false);
        setCurrentTopic({ name: '', questions: [] });
    };

    const handleDeleteTopic = (moduleIndex, topicIndex) => {
        const updatedModules = [...modulesArray];
        updatedModules[moduleIndex].topics.splice(topicIndex, 1);
        setModulesArray(updatedModules);
    };

    const handleDeleteQuestion = (moduleIndex, topicIndex, questionIndex) => {
        const updatedModules = [...modulesArray];
        updatedModules[moduleIndex].topics[topicIndex].questions.splice(questionIndex, 1);
        setModulesArray(updatedModules);
    };

    const stableMcqModule = React.useMemo(() => ({
        toolbar: {
            container: [
                ["bold", "italic", "underline"],
                [{ list: "ordered" }, { list: "bullet" }],
                [{ script: "sub" }, { script: "super" }],
                [{ header: [1, 2, 3, false] }],
                ["image"],
                [{ color: [] }, { background: [] }],
                [{ align: [] }],
                ["clean"],
            ],
            handlers: {
                image: () => {
                    const input = document.createElement("input");
                    input.type = "file";
                    input.accept = "image/*";
                    input.click();

                    input.onchange = () => {
                        if (input.files?.length) {

                            const activeElement = document.activeElement;
                            const quillContainer = activeElement.closest('.ql-container');
                            if (quillContainer) {
                                const quillInstance = quillContainer.__quill;
                                if (quillInstance) {
                                    handleFiles(input.files, { current: { getEditor: () => quillInstance } });
                                }
                            }
                        }
                    };
                },
            },
        },
        clipboard: { matchVisual: false },
    }), []);

    const updateMcqRefs = React.useCallback((optionsLength) => {
        while (quillRefs.mcqOptions.current.length < optionsLength) {
            quillRefs.mcqOptions.current.push(React.createRef());
        }

    }, []);

    const getMcqModulesArray = React.useCallback((optionsLength) => {
        updateMcqRefs(optionsLength);
        return Array(optionsLength).fill(stableMcqModule);
    }, [updateMcqRefs, stableMcqModule]);



    React.useEffect(() => {
        if (!quillRefs.mcqOptions.current) {
            quillRefs.mcqOptions.current = [];
        }
    }, []);

    const mcqModulesArray = React.useMemo(() => {
        return getMcqModulesArray(satValues?.mcqOptions?.length);
    }, [satValues?.mcqOptions?.length, getMcqModulesArray]);


    return (
        <>
            <Page title="Add Neet">
                <PageHeader pageTitle="Add Neet" />
                <Grid container spacing={2} className='GACognitivesection' sx={{ mb: 2, padding: '15px 20px' }}>
                    <Grid item xs={12} sm={3} sx={{ marginBottom: '18px', paddingRight: '18px' }}>
                        <TextField
                            variant="outlined"
                            inputProps={{ maxLength: 50 }}
                            fullWidth
                            id="addname"
                            label="Name*"
                            type="search"
                            value={name}
                            onChange={(e) => { setName(e.target.value); setNameError(''); }}
                            sx={{
                                // bgcolor: "#f0f0f0",
                                borderRadius: 1,
                                height: 36,
                                '& .MuiInputBase-input': {
                                    fontSize: 14,
                                    padding: "8px 12px",

                                },
                            }}
                            error={!!nameError}
                        />
                        {nameError && <FormHelperText error>{nameError}</FormHelperText>}
                    </Grid>


                    <Grid item xs={12} sm={6} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                        <InputLabel id="complexity-level-label">Time</InputLabel>
                        <Grid container className='AssessmentTime' spacing={2}>
                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Hours</InputLabel>
                                    <Select
                                        name="hours"
                                        value={time.hours}
                                        // disabled
                                        onChange={handleChange}
                                        label="Hours"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 158,
                                                }
                                            }
                                        }}
                                    >
                                        {hoursArray.map((hour) => (
                                            <MenuItem key={hour} value={hour}>{hour}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Minutes</InputLabel>
                                    <Select
                                        name="minutes"
                                        value={time.minutes}
                                        onChange={handleChange}
                                        // disabled
                                        label="Minutes"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 158,
                                                }
                                            }
                                        }}
                                    >
                                        {minutesArray.map((minute) => (
                                            <MenuItem key={minute} value={minute}>{minute}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Seconds</InputLabel>
                                    <Select
                                        name="seconds"
                                        value={time.seconds}
                                        onChange={handleChange}
                                        // disabled
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 228,
                                                }
                                            }
                                        }}
                                    >
                                        {secondsArray.map((second) => (
                                            <MenuItem key={second} value={second}>{second}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </Grid>


                    {/* <Grid item xs={3} fullWidth>
                        <FormControl className={classes.formControl} style={{ width: "100%" }}>
                            <InputLabel id="demo-simple-select-standard-label">NEET Type*</InputLabel>
                            <Select
                                // disabled={submitted}
                                name="neetType"
                                labelId="demo-simple-select-standard-label"
                                id="neetType"
                                label="neetType"
                                value={neetType}
                                onChange={(e) => setNeetType(e.target.value)}
                                displayEmpty
                                error={!!neetTypeError}

                            >
                                <MenuItem value="Chemistry">Chemistry</MenuItem>
                                <MenuItem value="Physics">Physics</MenuItem>
                                <MenuItem value="Biology">Biology</MenuItem>
                            </Select>
                        </FormControl>

                        {neetTypeError && <FormHelperText error>{neetTypeError}</FormHelperText>}
                    </Grid> */}









                    <Grid className="unique" item xs={12} sm={6} sx={{ marginBottom: '0px', paddingRight: '18px' }}>
                        {thumbPreview === null ? (
                            <FormControl style={{ height: '100%' }}
                                required
                                component="fieldset"
                                color="primary"
                                variant="outlined"
                                fullWidth
                                name="thumbImage"
                            >
                                <Typography variant="subtitle1">Thumb Image* <span style={{
                                    fontSize: '12px',
                                    float: 'inline-end', paddingBottom: '0', marginBottom: '0', position: 'relative', top: '5px'
                                }}>required resolution (360X200)</span></Typography>
                                <DropzoneArea className="dropTextArea"
                                    acceptedFiles={['image/jpeg', 'image/png', 'image/bmp']}
                                    showPreviews={false}
                                    dropzoneText="Drag and Drop Image or Browse File"
                                    showPreviewsInDropzone={false}
                                    maxFileSize={300000000}
                                    filesLimit={1}
                                    showAlerts={false}
                                    styles={{
                                        height: '100%', minHeight: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'center'
                                    }}
                                    onChange={handleFileChange}
                                    useChipsForPreview
                                    previewGridProps={{ container: { spacing: 1, direction: 'row' } }}
                                    showFileNamesInPreview
                                />
                                {ImageError && <FormHelperText error>{ImageError}</FormHelperText>}
                            </FormControl>


                        ) : (
                            <div className={classes.imgPreviewRoot}>
                                <Typography variant="subtitle1">Thumb Image</Typography>
                                <Badge
                                    badgeContent={
                                        <CloseIcon id='ThumbPreview'
                                            className={classes.badgeAlign}
                                            onClick={() => {
                                                setThumbPreview(null);
                                                setThumbImage(null);
                                            }}
                                        />
                                    }
                                >
                                    <Avatar
                                        variant="rounded"
                                        src={thumbPreview}
                                        style={{ minHeight: '150px !important' }}
                                        className={thumbPreview !== null && classes.fileImgSIze}
                                    />
                                </Badge>
                            </div>
                        )}
                    </Grid>


                    <Grid item xs={12} sm={6} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                        <Typography variant="subtitle1">Neet Description *</Typography>
                        {descriptionError && (
                            <FormHelperText error sx={{ marginTop: '4px', marginLeft: '8px' }}>
                                {descriptionError}
                            </FormHelperText>
                        )}
                        <ReactQuill

                            theme="snow"
                            id='questionText'
                            name="question"
                            defaultValue={editorValue}
                            onChange={(content) => {
                                if (content.length > 255) {
                                    handleChangeDescription(content.slice(0, 255));
                                } else {
                                    handleChangeDescription(content);
                                }
                            }}
                            onPaste={(e) => {
                                e.preventDefault();
                                const clipboardText = e.clipboardData.getData('text').slice(0, 255);
                                handleChangeDescription(clipboardText);
                            }}
                            // fullWidth
                            style={{ height: '150px', marginBottom: '30px' }}
                        />


                    </Grid>



                    {modulesArray?.map((module, moduleIndex) => (
                        <Box key={moduleIndex} sx={{ mb: 3, border: '1px solid #eee', p: 2, borderRadius: 1, width: '100%', mt: 3 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="h6" sx={{ p: '5px' }}>{module.name}</Typography>
                                <Box>
                                    <IconButton onClick={() => handleEditModule(moduleIndex)}>
                                        <EditIcon />
                                    </IconButton>
                                    <IconButton id='VisibilityIcon' style={{ height: '40px' }} onClick={() => handlePreviewOpen(module, moduleIndex)} color="error">
                                        < VisibilityIcon />
                                    </IconButton>
                                </Box>
                            </Box>

                            <IconButton
                                size="small"
                                onClick={() => {

                                    setCurrentTopicIndex(moduleIndex);
                                    setOpenDialog(true);
                                    setSelectedOption('create');
                                    setSelectedQuestions([]);
                                    setSatValues({
                                        question: '',
                                        mcqOptions: [{ option: '', isCorrect: false }]
                                    });
                                    setSelectedSkills('');
                                    setQuestionError({});
                                }} sx={{ color: 'error.main' }}
                            ><AddCircleIcon /></IconButton>
                            {/* 
                            <Button
                                variant="outlined"
                                startIcon={<AddCircleIcon />}
                                onClick={() => {
                                    setCurrentModuleIndex(moduleIndex);
                                    setCurrentTopic({ name: '', questions: [] });
                                    setOpenTopicDialog(true);
                                    setIsEditingTopic(false);
                                }}
                                sx={{ mt: 1, ml: 3, mb: 1 }}
                            >
                                Add Topic
                            </Button> */}
                        </Box>
                    ))}



                    <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'end', paddingTop: '15px' }}>
                        {/* <Button id='AddEnglish' sx={{ marginRight: '15px', paddingBottom: '0px' }} variant="contained" color="primary" onClick={() => handleDialogOpen("Neet")}>
                            Add Modules
                        </Button> */}







                        <Stack direction="column" spacing={2} sx={{ marginRight: '15px', mt: 2 }}>
                            <Button
                                onClick={handleAddModule}
                                variant="contained"
                                startIcon={<AddCircleIcon />}
                            >
                                Add Chapter
                            </Button>
                            <LoadingButton
                                type="submit"
                                id="addassessmentgeneral"
                                onClick={handleSubmit}
                                variant="contained"
                                color="primary"
                                loading={loading}
                            >
                                Submit
                            </LoadingButton>
                        </Stack>

                    </Grid>
                    {errorMessage && (
                        <Alert severity="error" sx={{ marginBottom: '10px' }}>
                            {errorMessage}
                        </Alert>
                    )}
                    {errorMessageAll && (
                        <Alert severity="error" sx={{ marginBottom: '10px' }}>
                            {errorMessageAll}
                        </Alert>)}
                </Grid>






                <Dialog open={openDialog} onClose={handleCloseNew} fullWidth>
                    <DialogTitle style={{ paddingBottom: '0px' }}>Add Question</DialogTitle>
                    <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>
                        <FormControl component="fieldset">
                            <RadioGroup
                                row
                                value={selectedOption}
                                onChange={handleChangeOption}
                            >
                                <FormControlLabel id='createRadioIs' value="create" control={<Radio />} sx={{ marginRight: '40px' }} label="Create Question" />
                                <FormControlLabel id='selectRadioIs' value="select" control={<Radio />} label="Select Question" />
                            </RadioGroup>
                        </FormControl>

                        {selectedOption === 'select' ?
                            <Grid container spacing={2}>
                                <Grid item sm={12}>
                                    <FormControl fullWidth>
                                        <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                            Select Level of Objective
                                        </Typography>
                                        <FormGroup className='FormCheck'>
                                            {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                <FormControlLabel
                                                    disabled={submitted}
                                                    key={skill}
                                                    id={skill}
                                                    control={
                                                        <Radio
                                                            id={`radioIsget${skill}`}
                                                            checked={selectedSkills === skill}
                                                            onChange={handleChangeCheck}
                                                            value={skill}
                                                        />
                                                    }
                                                    label={skill}
                                                />
                                            ))}
                                        </FormGroup>
                                        {questionError && questionError.Objective && <FormHelperText error>{questionError?.Objective}</FormHelperText>}
                                    </FormControl>
                                </Grid>

                                <Grid item xs={3} fullWidth>
                                    <FormControl className={classes.formControl} style={{ width: "100%" }}>
                                        <InputLabel id="demo-simple-select-standard-label">Type*</InputLabel>
                                        <Select
                                            // disabled={submitted}
                                            name="questionType"
                                            labelId="demo-simple-select-standard-label"
                                            id="questionType"
                                            label="questionType"
                                            value={details.questionType}
                                            onChange={(e) => setDetails(prevDetails => ({
                                                ...prevDetails,
                                                questionType: e.target.value
                                            }))}
                                            displayEmpty
                                        >
                                            <MenuItem value="Chemistry">Chemistry</MenuItem>
                                            <MenuItem value="Physics">Physics</MenuItem>
                                            <MenuItem value="Biology">Biology</MenuItem>
                                        </Select>
                                    </FormControl>

                                </Grid>

                                <Grid item xs={12}>
                                    <div className="search-select-container">
                                        <div className="search-select-container" style={{ display: 'flex' }}>
                                            {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                                <KeyboardDoubleArrowLeftIcon />
                                            </IconButton>}
                                            <input
                                                type="text"
                                                // readOnly={submitted}
                                                onChange={CreateQuestion}
                                                placeholder="Search or Select"
                                                aria-label="Search or select an option"
                                                style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                            />
                                            <IconButton disabled={questionList?.length === 0} aria-label="Next" onClick={handleRightArrowClick}>
                                                <KeyboardDoubleArrowRightIcon />
                                            </IconButton>
                                        </div>

                                        {loadingQuestion &&
                                            <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                                Loading.....
                                            </Typography>
                                        }

                                        {visible && !loadingQuestion && (
                                            <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                                {questionList && questionList.length > 0 ? (
                                                    questionList.map((item) => {
                                                        const sanitizedQuestion = item.question_text?.replace(/<p>/g, '<span style="display: flex;">')
                                                            .replace(/<\/p>/g, '</span>')
                                                            .replace(/&nbsp;/g, ' ');
                                                        const isSelected = selectedQuestions?.some(q => q.id === item.id);

                                                        const selectedquestioncheck = modulesArray && modulesArray.map((data) => {
                                                            return data?.questions?.some(q => q === item.id);
                                                        });

                                                        const checkingselected = selectedquestioncheck?.includes(true)

                                                        return (
                                                            <li
                                                                key={item.id}
                                                                role="option"
                                                                className="dropdown-item"
                                                                tabIndex={0}
                                                                aria-selected={selectedOptionnew?.id === item.id}
                                                                style={{
                                                                    backgroundColor: isSelected || checkingselected ? '#e0ffe0' : 'transparent',
                                                                    padding: '8px',
                                                                    borderRadius: '4px',
                                                                    marginBottom: '4px',
                                                                }}
                                                            >
                                                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                    <span dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />
                                                                    {!isSelected && checkingselected !== true ? (
                                                                        <Button
                                                                            id="questionadd"
                                                                            variant="outlined"
                                                                            color="primary"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleAddQuestion(item);
                                                                            }}
                                                                            sx={{
                                                                                fontSize: '0.75rem',
                                                                                minWidth: '24px',
                                                                                minHeight: '24px',
                                                                                padding: '2px',
                                                                                borderRadius: '12px',
                                                                            }}
                                                                        >
                                                                            +
                                                                        </Button>
                                                                    ) : (
                                                                        <Typography variant="caption" sx={{
                                                                            fontStyle: 'italic',
                                                                            marginRight: '8px',
                                                                            color: checkingselected ? '#666' : '#4caf50'
                                                                        }}>
                                                                            {checkingselected ? 'Already Selected' : 'Selected'}
                                                                        </Typography>
                                                                    )}
                                                                </div>
                                                            </li>
                                                        );
                                                    })
                                                ) : (
                                                    <li className="dropdown-item" role="option">
                                                        No results found
                                                    </li>
                                                )}
                                            </ul>
                                        )}

                                        {!loadingQuestion && selectedQuestions.length > 0 && (
                                            <>
                                                <Typography>Selected Questions:</Typography>
                                                {selectedQuestions.map((item, index) => (
                                                    <Tooltip key={index} title={item.passage || ''} placement="top">
                                                        <div style={{ display: 'flex' }}>
                                                            <Typography
                                                                id="QuestionArea"
                                                                dangerouslySetInnerHTML={{ __html: item.question_text }}
                                                            />
                                                            <Button onClick={() => handleDeselect(item.id)}>
                                                                x
                                                            </Button>
                                                        </div>
                                                    </Tooltip>
                                                ))}
                                            </>
                                        )}
                                    </div>
                                </Grid>
                            </Grid>
                            :
                            <Grid container spacing={2}>
                                <Grid item sm={12}>
                                    <FormControl fullWidth>
                                        <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                            Select Level of Objective
                                        </Typography>
                                        <FormGroup className='FormCheck'>
                                            {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                <FormControlLabel
                                                    // disabled={submitted}
                                                    key={skill}
                                                    id={`Label${skill}`}
                                                    control={
                                                        <Radio
                                                            // readOnly={submitted}
                                                            id={skill}
                                                            checked={selectedSkills === skill}
                                                            onChange={handleChangeCheck}
                                                            value={skill}
                                                        />
                                                    }
                                                    label={skill}
                                                />
                                            ))}
                                        </FormGroup>
                                        {questionError && questionError.Objective && <FormHelperText error>{questionError?.Objective}</FormHelperText>}
                                    </FormControl>
                                </Grid>

                                <Grid item xs={3} fullWidth>
                                    <FormControl className={classes.formControl} style={{ width: "100%" }}>
                                        <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                        <Select
                                            // disabled={submitted}
                                            name="level"
                                            labelId="demo-simple-select-standard-label"
                                            id="level"
                                            label="Level"
                                            value={details.level}
                                            onChange={(e) => setDetails(prevDetails => ({
                                                ...prevDetails,
                                                level: e.target.value
                                            }))}
                                            displayEmpty
                                        >
                                            <MenuItem value="easy">Easy</MenuItem>
                                            <MenuItem value="medium">Medium</MenuItem>
                                            <MenuItem value="complex">Complex</MenuItem>
                                        </Select>
                                    </FormControl>
                                </Grid>


                                <Grid item xs={3} fullWidth>
                                    <FormControl className={classes.formControl} style={{ width: "100%" }}>
                                        <InputLabel id="demo-simple-select-standard-label">Type*</InputLabel>
                                        <Select
                                            // disabled={submitted}
                                            name="questionType"
                                            labelId="demo-simple-select-standard-label"
                                            id="questionType"
                                            label="questionType"
                                            value={details.questionType}
                                            onChange={(e) => setDetails(prevDetails => ({
                                                ...prevDetails,
                                                questionType: e.target.value
                                            }))}
                                            displayEmpty
                                        >
                                            <MenuItem value="Chemistry">Chemistry</MenuItem>
                                            <MenuItem value="Physics">Physics</MenuItem>
                                            <MenuItem value="Biology">Biology</MenuItem>
                                        </Select>
                                    </FormControl>

                                </Grid>

                                <Grid item xs={6} fullWidth>
                                    <FormControl className={classes.formControl} style={{ width: "100%" }}>
                                        <InputLabel id="demo-simple-select-standard-label">Study Materials</InputLabel>
                                        <Select
                                            // disabled={submitted}
                                            name="studyMaterials"
                                            labelId="demo-simple-select-standard-label"
                                            id="studyMaterials"
                                            label="Study Materials"
                                            value={details.studyMaterials}
                                            onChange={(e) => setDetails(prevDetails => ({
                                                ...prevDetails,
                                                studyMaterials: e.target.value
                                            }))}
                                            displayEmpty
                                            multiple
                                        >
                                            <MenuItem value="Previous year questions">Previous year questions</MenuItem>
                                            <MenuItem value="Practice questions">Practice questions</MenuItem>
                                            <MenuItem value="Exampler questions">Exampler questions</MenuItem>
                                        </Select>
                                    </FormControl>
                                </Grid>




                                {(details?.questionType === 'Chemistry' || details?.questionType === 'Physics' || details?.questionType === 'Biology') &&
                                    <>
                                        <div>
                                            <Grid item xs={12}>
                                                <FormControl className={classes.formControl}>
                                                    <Typography className={classes.background} gutterBottom variant="subtitle1">
                                                        Create Question*
                                                    </Typography>
                                                    <QuillEditor
                                                        ref={quillRefs.question}
                                                        // readOnly={submitted}
                                                        theme="snow"
                                                        id={`questionText`}
                                                        name="question"
                                                        defaultValue={satValues.question}
                                                        onChange={(value) => handleChangeQuestionEnglish('question', value)}
                                                        modules={questionModules}
                                                        formats={formats}
                                                        fullWidth
                                                    />
                                                    {questionError && questionError.question && <FormHelperText error>{questionError?.question}</FormHelperText>}
                                                </FormControl>
                                            </Grid>

                                            {satValues?.mcqOptions?.map((opt, index) => (
                                                <div key={index} style={{ position: 'relative', marginTop: '10px' }}>
                                                    <Grid container spacing={2} alignItems="center">
                                                        <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                                            <QuillEditor
                                                                ref={quillRefs.mcqOptions.current[index]}
                                                                theme="snow"
                                                                id={`optiontext`}
                                                                name={`mcqQuestion`}
                                                                value={opt.option}
                                                                onChange={(value) => { handleMCQOptionChangeEnglish(index, 'option', value); setQuestionError({ option: '', }) }}
                                                                modules={mcqModulesArray[index]}
                                                                formats={formats}
                                                                placeholder="Option"
                                                                style={{ marginTop: 10, flex: 1 }}
                                                            />
                                                            <IconButton
                                                                // readOnly={submitted}
                                                                aria-label="delete"
                                                                color="error"
                                                                onClick={() => handleRemoveOptionEnglish(index)}
                                                                style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                                            >
                                                                <ClearIcon fontSize="small" id="ClearsIcon" />
                                                            </IconButton>
                                                            <FormControlLabel
                                                                control={
                                                                    <Checkbox
                                                                        name={`mcqOptionsisCorrect`}
                                                                        checked={opt.isCorrect}
                                                                        onChange={() => handleMCQOptionChangeEnglish(index, 'isCorrect', !opt.isCorrect)}
                                                                        disabled={!opt.option.trim()}
                                                                    />
                                                                }
                                                                label="Correct"
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                </div>
                                            ))}

                                            {questionError && questionError.option && <FormHelperText error>{questionError?.option}</FormHelperText>}
                                            {questionError && questionError.correctAnswer && <FormHelperText error>{questionError?.correctAnswer}</FormHelperText>}

                                            <Button
                                                id='AddoptionsIn'
                                                // disabled={submitted}
                                                variant="contained"
                                                color="primary"
                                                onClick={handleAddOptionEnglish}
                                                style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                                            >
                                                Add Option
                                            </Button>
                                            {requirederrors.questionid && (
                                                <FormHelperText error>{requirederrors.questionid}</FormHelperText>
                                            )}
                                        </div>
                                        <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                            <Typography variant="subtitle1">Explanation *</Typography>
                                            <QuillEditor
                                                ref={quillRefs.explanation}
                                                theme="snow"
                                                id="explanation"
                                                name="explanation"
                                                modules={explanationModules}
                                                formats={formats}
                                                value={explanation}
                                                onChange={(content) => {
                                                    satExplanation(content);
                                                    setQuestionError({
                                                        explanation: ''
                                                    });
                                                }}
                                            />
                                            {questionError && questionError.explanation && <FormHelperText error>{questionError?.explanation}</FormHelperText>}
                                        </Grid>
                                    </>
                                }

                                <Grid item xs={12}>
                                    <Button
                                        onClick={SubmitQuestion}
                                        id={buttonText}
                                        type="submit"
                                        disabled={loadingnew}
                                        variant="contained"
                                        color="primary"
                                        fullWidth
                                    >
                                        {buttonText}
                                    </Button>
                                </Grid>
                                {requirederrors.questionid && (
                                    <FormHelperText error>{requirederrors.questionid}</FormHelperText>
                                )}
                            </Grid>
                        }
                    </DialogContent>

                    <DialogActions>

                        {selectedOption === 'select' &&
                            <Button
                                id='btnSubmited'
                                onClick={() => {
                                    const updatedModules = [...modulesArray];
                                    const currentModule = updatedModules[currentTopicIndex];

                                    selectedQuestions.forEach(question => {
                                        currentModule.questions.push(question.id);
                                    });

                                    setModulesArray(updatedModules);
                                    setOpenDialog(false);
                                    setSelectedQuestions([]);

                                }}

                                color="secondary"
                            >
                                Submit
                            </Button>}



                        <Button
                            id='btnCanceled'
                            onClick={() => {
                                setOpenDialog(false);
                                setSelectedQuestions([]);
                            }}
                            color="primary"
                        >
                            Cancel
                        </Button>
                    </DialogActions>
                </Dialog>



                <Dialog open={Preview} onClose={handlePreview} fullWidth
                    sx={{
                        '& .MuiDialog-paper': {
                            maxHeight: '75vh !important',
                            overflow: 'hidden !important'
                        }
                    }}>
                    <DialogTitle>Preview</DialogTitle>
                    <DialogContent style={{ marginLeft: '18px' }}>
                        <ol type="1" style={{ paddingLeft: '2em', margin: 0 }}>
                            {previewData ? previewData?.questiondetails?.length > 0 && previewData?.questiondetails?.map((details, index) => {
                                const sanitizedQuestion = details?.question_text
                                    .replace(/<p>/g, '<span style="display: flex;">')
                                    .replace(/<\/p>/g, '</span>')
                                    .replace(/&nbsp;/g, ' ');
                                return (
                                    <li id="QuestionlistMakers">
                                        <Box style={{ display: 'flex', alignItems: 'center', marginBottom: '10px', justifyContent: 'space-between' }}>
                                            <Typography
                                                variant="para" style={{ lineHeight: '1.2', fontSize: '15px', fontWeight: '400', paddingRight: '15px' }}
                                                dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                            />

                                            {/* {previewData && previewData?.type === 'Maths' && <IconButton id='EditIcon' onClick={() => handleEditIndividualQuestion(details?.question_text, previewData?.questions_list[index])} color="error">
                                            <EditIcon />
                                        </IconButton>} */}
                                            <IconButton onClick={() => DeleteQuestion(details.id, index)} color="error">
                                                < DeleteOutlinedIcon id={`DeleteIcon${index}`} style={{ color: "#ff4842" }} />
                                            </IconButton >
                                        </Box>
                                    </li>
                                )
                            })
                                :
                                <div style={{ textAlign: 'center', margin: '20px 0' }}>
                                    <CircularProgress />
                                </div>
                            }
                            {previewData && previewData?.questions_list?.length === 0 &&
                                <div style={{ textAlign: 'center', marginTop: '20px' }}>
                                    <Typography
                                        variant="para"
                                        style={{
                                            lineHeight: '1.2',
                                            fontSize: '15px',
                                            fontWeight: '400',
                                            paddingRight: '15px',
                                            display: 'inline-block'
                                        }}
                                    >
                                        No Questions Found
                                    </Typography>
                                </div>

                            }
                        </ol>
                    </DialogContent>

                    <DialogActions>
                        <Button id='btnCancelIs' onClick={handlePreview} color="primary">
                            Cancel
                        </Button>
                        <Button id='btnUpdateIs' onClick={() => handleUpdateQuestions(editIndexnew)} color="primary">
                            Update
                        </Button>
                    </DialogActions>
                </Dialog>
                <Dialog
                    open={openModuleDialog}
                    onClose={() => setOpenModuleDialog(false)}
                    fullWidth
                    maxWidth="sm"
                >
                    <DialogTitle>{currentModuleIndex !== null ? 'Update Chapter' : 'Add Chapter'}</DialogTitle>
                    <DialogContent>
                        <TextField
                            fullWidth
                            label="Chapter Name"
                            value={currentModule.name}
                            onChange={(e) => setCurrentModule({ ...currentModule, name: e.target.value })}
                            margin="normal"
                            sx={{ mt: 2 }}
                        />


                    </DialogContent>
                    <DialogActions>

                        <Button onClick={handleModuleSubmit} color="primary">
                            {currentModuleIndex !== null ? 'Update' : 'Create'} Chapter
                        </Button>
                    </DialogActions>
                </Dialog>



                <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />


            </Page>
        </>
    );

}

const useStyles = makeStyles(() => ({
    imgPreviewRoot: {
        borderRadius: '10px',
        padding: '0.8rem;',
    },
    fileImgSIze: {
        width: '100%',
        height: '120px',
        objectFit: 'cover',
        objectPosition: 'center',
        border: '1px solid #fff',
        borderRadius: '5px',
        boxShadow: '0 3px 10px rgb(0 0 0 / 20%)',
    },
    badgeAlign: {
        boxShadow: '0 2px 8px -5px #ff0000',
        color: '#FF0000',
        fontSize: '1.2rem',
        backgroundColor: '#fff',
        padding: '2px',
        borderRadius: '10px',
        cursor: 'pointer',
    },
    deleteLabel: {
        width: 'max-content',
        cursor: 'pointer',
    },
    'quill-error': {
        '& .ql-container': {
            border: '1px solid red !important',
            borderRadius: '4px',
        },
    },

}));
export default NEET;