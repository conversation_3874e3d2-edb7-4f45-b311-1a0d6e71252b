/* eslint-disable no-else-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect,useMemo } from 'react';
import Card from '@mui/material/Card';
import { Box, CardMedia, Button, Chip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { useNavigate } from 'react-router-dom';
import { makeStyles } from '@mui/styles';
import PersonIcon from '@mui/icons-material/Person';
import clsx from 'clsx';
import CardContent from '@mui/material/CardContent';
import moment from 'moment';
import { useSelector } from 'react-redux';
import { LoadingButton } from '@mui/lab';
import Background from '../../../assets/logo/background.svg';
import CategaryLogo from '../../../assets/Images/category.png'


export default function B2BCard(props) {
  const progressValue = Number(props?.progress?.props.value);

  const ProgressChip = ({ progressValue }) => {
    const { label, chipStyle } = useMemo(() => {
      if (progressValue === 0) {
        return {
          label: "Not Started",
          chipStyle: {
            backgroundColor: "#dedbdb",
            color: "#333",
            height: '22px',
            fontWeight: '400',
            borderRadius: '2px',
          }
        };
      } else if (progressValue === 100) {
        return {
          label: "Completed",
          chipStyle: {
            backgroundColor: "#14ae5c",
            color: "#fff",
            height: '22px',
            fontWeight: '400',
            borderRadius: '2px',

          }
        };
      } else {
        return {
          label: "In Progress",
          chipStyle: {
            backgroundColor: "#ffd452",
            color: "#333",
            height: '22px',
            fontWeight: '400',
            borderRadius: '2px',

          }
        };
      }
    }, [progressValue]); 
  
    return (
      <Chip
        label={label}
        size="small"
        id="chipLabel"
        sx={chipStyle}
      />
    );
  };
  

  const classes = useStyles();
  const [status, setStatus] = useState(null);

  useEffect(() => {
    setStatus('');
    if (props?.trial && props?.planStatus?.toLowerCase() === 'expired') {
      setStatus('Trial Ended');
    } else if (props?.trial) {
      setStatus('Trial');
    } else if (props?.subscribed && props?.planStatus?.toLowerCase() === 'expired') {
      setStatus('Subscription Ended');
    } else if (props?.subscribed) {
      setStatus('Subscribed');
    }
  }, [props]);

  const LightTooltip = styled(({ className, ...props }) => <Tooltip {...props} classes={{ popper: className }} />)(
    ({ theme }) => ({
      [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.common.white,
        color: 'rgba(0, 0, 0, 0.87)',
        boxShadow: theme.shadows[2],
        fontSize: 11,
      },
    })
  );

  const splitText = (text, maxWords) => {
    const words = text.split(' ');
    if (words.length > maxWords) {
      const firstLine = words.slice(0, maxWords).join(' ');
      const secondLine = words.slice(maxWords).join(' ');
      return [firstLine, secondLine];
    }
    return [text];
};

  const lines = splitText(props.title, 30);

  return (
    <div>
      <Card
        className="cardhover"
        onClick={() => props.handleCardClick()}
        sx={{
          // cursor: 'pointer',
          minHeight: '400px',
          maxHeight: '400px',
          maxWidth: '95% !important',
          display: 'flex',
          flexDirection: 'column',

          transition: 'transform 0.3s ease-in-out, max-height 0.3s ease-in-out', // Add transition for max-height
          '&:hover': {
            transform: {xs: 'none', sm: 'none', md: 'scale(1.05)', lg: 'scale(1.05)', xl: 'scale(1.05)'},
            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.2)',
          },
        }}
      >
        <Box
          sx={{
            position: 'relative',
            backgroundColor: '#00B673',
            color: 'white',
            textAlign: 'center',
            paddingTop: '8px',
            paddingBottom: '8px',
          }}
        >
          <Typography variant="h6" component="div">
            {props?.data?.type && props?.data?.type?.charAt(0).toUpperCase() + props?.data?.type?.slice(1).toLowerCase()}
          </Typography>
        </Box>

        <CardMedia
          component="img"
          image={props.image}
          sx={{
            height: '160px',
            width: '100%',
            objectFit: 'cover',
            minHeight: '160px',
            maxHeight: '160px',
            cursor: 'pointer',
          }}
          alt="course image"
          className="card-image"



        />

        <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', padding: '9px 12px', }}>
          {/* <Box sx={{ minHeight: '30px', display: 'flex', alignItems: 'center' }}>
            {status && (
              <LightTooltip
                title={
                  <Box>
                    <Typography variant="body2">
                      Valid from: {moment(props.data?.validFrom).format('MM-DD-YYYY')}
                    </Typography>
                    <Typography variant="body2">
                     Valid to: {moment(props.data?.validTo).format('MM-DD-YYYY')}
                    </Typography>
                    <Typography variant="body2">
                      Days left:{props.data?.remainingDays} {"Days"}
                    </Typography>
                  </Box>
                }
                placement="top-start"
              >
                <Typography
                  variant="body2"
                  color="secondary"
                  width="max-content"
                  sx={{ alignItems: 'center', display: 'flex' }}
                >
                  {status} &nbsp;
                  <Iconify icon="bi:info-circle" sx={{ color: '#5f5b5b' }} />
                </Typography>
              </LightTooltip>
            ) }
          </Box> */}

          <Box style={{ height: "18px" }}>
          <ProgressChip progressValue={progressValue} />
            {/* <Chip label={progressValue === 100 ? "Completed" : "In Progress"} size="small" id="chipLabel" sx={{
              background: progressValue === 100 ? "#14ae5c" : '#c6f3d4',
              color: progressValue === 100 ? "#fff" : "#000",
              borderRadius: '2px', fontWeight: '400',
              textTransform: 'capitalize',
              width: 'fit-content',
              fontSize: '12px',
            }} /> */}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', pr: 5 }}>
  {props?.data?.title?.length > 25 ? (
            <Tooltip title={props?.data?.title} arrow>
            <Typography
              variant="body2"
              
              style={{
                fontFamily: 'Poppins',
                fontSize: {sm:'12px', md:'14px', lg:'16px' },
                fontWeight: '500',
                color: "#000",
                position: 'relative',
                top: '12px' ,
                lineHeight: '1.2', 
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                height: '35.5px',
                webkitLineClamp: 2,
                webkitBoxOrient: 'vertical'   
              }}
              className={classes.coursetitle}
              gutterBottom>
          {props?.data?.title}
            </Typography> 
            </Tooltip>
          ) : (
          <Typography
              variant="body2"
              
              style={{
                fontFamily: 'Poppins',
                fontSize: {sm:'12px', md:'14px', lg:'16px' },
                fontWeight: '500',
                color: "#000",
                position: 'relative',
                top: '12px' ,
                lineHeight: '1.2', 
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                height: '35.5px',
                webkitLineClamp: 2,
                webkitBoxOrient: 'vertical'   
              }}
              className={classes.coursetitle}
              gutterBottom>
          {props?.data?.title}
            </Typography> 
            )}
          </Box>


          {props.author && (
            <Typography sx={{ color: '#323030', fontSize: '0.65rem', fontWeight: '600' }}>
              Author: {props.author.name}
            </Typography>
          )}

          {props.description && (
            <Typography
              variant="body2"
              color="text.secondary"
              dangerouslySetInnerHTML={{ __html: props.description }}
            />
          )}

        
          {props.progress && <Box>{props.progress}</Box>}

          {props.date && (
            <Typography gutterBottom color="#000" style={{color: "#000 !important"}} variant="body2">
              {props.date}
            </Typography>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex' }}>
              {props.userLicense && (
                <Typography variant="body2" color="#000">
                  User License <span className={classes.enrolledUser}> {props.userLicense}</span>{' '}
                </Typography>
              )}
              {props.userCount && (
                <Typography variant="body2" color="#000" sx={{ marginLeft: '12px', width: 'max-content' }}>
                  {props.userCount}
                </Typography>
              )}
            </Box>

            {props.status && (
              <div className={clsx({
                [classes.active]: props.status === 'Live' || props.status === 'LIVE',
                [classes.inActive]: props.status === 'ACTIVE',
                [classes.Expired]: props.status === 'INACTIVE',
                [classes.inActive]: props.status === 'Draft' || props.status === 'DRAFT',
              })}>
                <Typography variant="body2" color="#000" sx={{ fontSize: '1rem', fontWeight: '400' }}>
                  {props.status}
                </Typography>
              </div>
            )}
          </Box>

          {props.Enrolled && (
            <Box sx={{ marginTop: '1rem' }}>
              <Typography variant="body2" color="#000" sx={{ display: 'flex', alignItems: 'center' }}>
                <PersonIcon /> &nbsp; Enrolled Users &nbsp; {props.Enrolled}
              </Typography>
            </Box>
          )}

          {props.enrollDate && (
            <Typography color="#000" variant="body2" sx={{ fontSize: '11px' }}>
              Enrolled on {moment(props.enrollDate).format('LL')}
            </Typography>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '2px', marginBottom: '5px' }}>
            {props.category?.description && props.category?.description?.length > 18 ?
              <Tooltip title={props.category === 'testing' ? "Category Video" : props.category?.description} arrow>
                <Typography className={classes.button} color="secondary">
                  {props.category?.description}
                </Typography>
              </Tooltip>
              :
              <Typography className={classes.button} color="secondary">
                {props.category?.description}
              </Typography>}
            <Box className={classes.back} width={40} height={40}>
              <img className={classes.logo} src={props?.logo ? props?.logo : CategaryLogo} alt="logo" width={38} height={38} style={{ marginTop: '0px' }} />
            </Box>
          </Box>
          <Button variant="contained" style={{color:"#fff",backgroundColor:'#687fe5', '&:hover': { backgroundColor: '#fff', color: "#687fe5", border: "1px solid #687fe5" } }}  onClick={() => props.handleCardClick()}>{progressValue === 0 ? "Start Now":"Continue Learning"}</Button>

        </CardContent>
      </Card>
    </div>
  );
}
const useStyles = makeStyles((theme) => ({
  trial: {
    color: 'white',
    padding: '0px 6px 0px 6px ',
    background: 'grey',
  },
  coursetitle: {
    overflow: 'hidden',
    // color: 'black',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    [theme.breakpoints.down('md')]: {
      overflow: 'auto',
      WebkitLineClamp: 'initial',
      WebkitBoxOrient: 'initial',
    },


  },
  card: {
    border: '0.5px solid #DFDFDF',
    boxShadow: '0px 3px 6px  #0000001A',
    borderRadius: '6px',
    backgroundImage: `url(${Background})`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'bottom right',
    // cursor: 'pointer',
    marginTop: '16px',
    marginLeft: '10px',
    width: '95%',
    // width: '85%', // Reduced from 90% to 85%
    // height: 'auto', // You can add this if needed
    // maxHeight: '300px', // Add maxHeight to limit overall card height
  },
  date: {
    marginTop: '15px',
  },
  button: {
    backgroundColor: '#F8F5F4',
    padding: '4px 6px',
    borderRadius: '6px',
    width: 'max-content',
    maxWidth: '180px',
    fontSize: '12px',
    color: '#000',
    overflow: 'hidden',
    display: 'block',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis'
  },
  title: {
    marginTop: '10px',
    fontWeight: 'bold',
    fontSize: '15px',
  },
  cardTitle: {
    fontWeight: 'bold',
    fontSize: '15px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '-webkit-line-clamp': 1,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
  },
  cardTitlenew: {
    fontWeight: 'bold',
    fontSize: '15px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    minHeight: '50px',
    maxHeight: '50px',
  },
  logo: {
    boxShadow: '0px 3px 22px #00000029',
    padding: '4px',
    backgroundColor: '#fff',
    borderRadius: '6px',
    marginTop: '10px',
  },
  description: {
    minHeight: '38px',
    fontSize: '0.8rem !important',
    overflow: 'hidden !important',
    fontFamily: 'Inter',
    textOverflow: 'ellipsis',
    '-o-text-overflow': 'ellipsis',
    '-ms-text-overflow': 'ellipsis',
    '-moz-binding': "url('ellipsis.xml#ellipsis')",
    '-ms-webkit-line-clamp': 2,
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    '& span': {
      fontSize: '0.8rem !important',
      color: 'black !important',
      backgroundColor: 'unset !important',
      fontFamily: 'Inter !important',
    },
    '& p': {
      '&:nth-child(1)': {
        display: 'block !important',
      },
      '&:nth-child(even)': {
        display: 'none ',
      },
      '&:nth-child(odd)': {
        display: 'none ',
      },
    },
  },
  enrolledUser: {
    backgroundColor: '#EBFFF8',
    borderRadius: '6px',
    padding: '4px 12px',
    fontSize: '12px',
  },
  active: {
    backgroundColor: 'green',
    borderRadius: '6px',
    padding: '4px 12px',
  },
  inActive: {
    backgroundColor: 'yellow',
    borderRadius: '6px',
    padding: '4px 12px',
  },
  Expired: {
    backgroundColor: '#FF8282',
    borderRadius: '6px',
    padding: '4px 12px',
  },
  trialButton: {
    width: 110,
    borderRadius: '6px',
    fontSize: '12px',
    backgroundColor: 'white',
    '@media (max-width: 1400px)': {
      padding: '6px 3px',
      lineHeight: '1',
    },
    '&:hover': {
      backgroundColor: 'white',
    },
    marginRight: '8px',
  },
  // CourseButton: {
  //     width: 110,
  //     color: '#00B673 ! important',
  //     border: '1px solid #00B673 ! important',
  //     borderRadius: '6px',
  //     fontSize: '12px',
  //     // marginLeft: '1rem',
  //     backgroundColor: 'white',
  //     '&:hover': {
  //         backgroundColor: 'white',
  //     },
  // },
  clamptext: {
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'normal',
    minHeight: '2.4em',
    lineHeight: '1.2em'
    //    min-height: 2.4em;                 
    //    line-height: 1.2em;  
  },

  CourseButton: {
    width: 110,
    color: '#6a6e6b', // initial text color
    border: '1px solid #6a6e6b', // initial border color
    borderRadius: '6px',
    fontSize: '12px',
    backgroundColor: 'white',
    '&:hover': {
      color: '#00B673', // text color on hover
      border: '1px solid #00B673', // border color on hover
      backgroundColor: 'white',
    },
  },
  subscribeButton: {
    width: 110,
    color: '#00B673 ! important',
    border: '1px solid #00B673 ! important',
    borderRadius: '6px',
    fontSize: '12px',
    // marginLeft: '1rem',
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: 'white',
    },
  },
}));