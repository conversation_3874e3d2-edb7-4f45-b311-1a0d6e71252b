/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect,useLayoutEffect } from 'react';
import { Container, Grid, Typography, Box,Dialog, DialogActions, DialogContent, DialogTitle, Button } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { LoadingButton } from '@mui/lab';
import { Carousel } from 'react-responsive-carousel';
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { makeStyles } from '@mui/styles';
import CheckIcon from '@mui/icons-material/Check';
import ClearIcon from '@mui/icons-material/Clear';
import Page from '../../components/Page';
import SnackBar from '../../components/snackbar/snackbar';
import loginServices from '../../services/loginServices';
import LottieLoading from '../../components/LottieLoading';
import SuccessPage from '../../assets/Images/successPage.png';
import failPage from '../../assets/Images/fail.png';
import Logo from '../../assets/logo/logo.png';
import mailSent from '../../assets/logo/Email.svg';
import backArrow from '../../assets/logo/Arrow.svg';

const useStyles = makeStyles((theme) => ({
  formcontainer: {
    backgroundColor: '#fff',
    borderRadius: '15px',
    padding: '30px 30px 30px 30px',
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
    minHeight: '500px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  login: {
    fontWeight: '600 !important',
    fontFamily: 'Plus Jakarta Sans',
    color: '#000000',
    textAlign: 'center',
  },
  carouselContainer: {
    '& .carousel .slide': {
      background: 'transparent !important',
    },
  },
  slideImg: {
    objectFit: 'cover',
    height: '100%',
  },
}));

const EmailVerification = () => {
    const classes = useStyles();
    const location = useLocation();
    const navigate = useNavigate();

    const [emailID, setEmail] = useState('');
    const [token, setToken] = useState('');
    // eslint-disable-next-line no-unused-vars
    const [verificationStatus, setVerificationStatus] = useState(null);
    const [loading, setLoading] = useState(true);
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [statues, setStatus] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [response, setResponse] = useState('');
    const [loginFormHeight, setLoginFormHeight] = useState(500);

    // Image arrays
    const mailSentImages = [mailSent];


    useEffect(() => {
        const queryParams = new URLSearchParams(window.location.search);
        const tokenFromParams = queryParams.get('token');
        const rawEmail = queryParams.get('email');
        const emailFromParams = rawEmail?.replace(/ /g, '+') || '';
        const email = decodeURIComponent(emailFromParams);
        if (emailFromParams) setEmail(email);
        if (tokenFromParams) setToken(tokenFromParams);
    }, [location.search]);

     const handleVerifyClick = async () => {
        setLoading(true);        
        try {
             const res = await loginServices.verifyEmail(encodeURIComponent(emailID), token);
             setResponse(res.data==='This mail is alredy verified'?res.data:'')
            if (res.status === 200) {               
                setStatus(true)
            } else {
                setStatus(false)
            }
        } catch (error) {
            setVerificationStatus('Error: Something went wrong.');
            setSnackbarTitle('Error: Something went wrong.');
        }
        setLoading(false);
       

    };
    useLayoutEffect(()=>{
        if(emailID){
            handleVerifyClick();
        }
    },[emailID])

    const SendEmailVerification = async () =>{
        const data = {
            email:emailID
        }
        try {
          const res = await loginServices.sendEmail(data);
          if (res.ok) {
        //  setOpenSnackbar(true)
        //  setSnackbarTitle(res.data.message)
          } 
        } catch (error) {
          console.log(error);
        }
      }
    const handleGoHome = (() => {
        setIsModalOpen(true)
        SendEmailVerification()
    })
    const BackToLogIn = (() => {
        navigate('/login')
    })

    const closeModal = () => {
        setIsModalOpen(false);
        navigate('/login')
      };

      const { t } = useTranslation('translation');
    return (
        <div>
            <Page title={'Email Verification'} style={{ padding: '0px !important' }}>
                <Container maxWidth="xl" sx={{ padding: '0px !important' }}>
                    <Grid container sx={{ minHeight: '80vh', margin: 'auto', display: 'flex', justifyContent: 'center', alignItems: 'center', width:  {sm: '100%', md: '85%', lg: "85%", xl: '75%'}, marginTop: { lg: '90px', xl: '45px'} }}> 
                        <Grid item xs={12} md={4.5} sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            padding: '20px'
                        }}>
                            {!loading ? (
                                <Box>
                                    {statues && !response ? (
                                        // Email Verification Success
                                        <Box sx={{minHeight: '480px'}} className={classes.formcontainer}>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12}>
                                                    <img src={Logo} alt="logo" width="145" style={{ margin: 'auto', height: '48px', width: '102px' }} />
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: '20px' }}>
                                                        <Typography sx={{
                                                            backgroundColor: '#52c28c',
                                                            width: '35px',
                                                            height: '35px',
                                                            borderRadius: '50%',
                                                            marginBottom: '6px'
                                                        }}>
                                                            <CheckIcon sx={{color: '#fff', position: 'relative', top: '4px', left: "5px"}}/>
                                                        </Typography>
                                                        <Typography className={classes.login} sx={{
                                                            fontWeight: 600,
                                                            fontFamily: 'Plus Jakarta Sans',
                                                            marginBottom: '0px',
                                                            fontSize: { md: '22px', lg: '23px !important' }
                                                        }} variant="h4">
                                                            E-mail is verified!
                                                        </Typography>
                                                    </Box>
                                                    <Typography sx={{
                                                        textAlign: 'center',
                                                        color: '#000',
                                                        marginTop: '10px'
                                                    }}>
                                                        We have successfully verified your email ID! You can continue your learning journey with your registered email ID.
                                                    </Typography>
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <Button
                                                        sx={{
                                                            color: " #004222ED !important",
                                                            width: '100%',
                                                            marginTop: '8px',
                                                            '&:hover': { background: 'none !important' }
                                                        }}
                                                        onClick={BackToLogIn}
                                                    >
                                                        <span style={{
                                                            fontSize: "15px",
                                                            fontWeight: '600',
                                                            marginRight: '5px',
                                                            marginBottom: '2px'
                                                        }}>
                                                            <img src={backArrow} alt="Back" style={{
                                                                width: "22px",
                                                                height: "20px",
                                                                position: 'relative',
                                                                right: '4px',
                                                                top: '2px'
                                                            }} />
                                                        </span>
                                                        {t('Back to Login')}
                                                    </Button>
                                                </Grid>
                                            </Grid>
                                        </Box>
                                    ) : (
                                        // Email Verification Failed
                                        <Box className={classes.formcontainer}>
                                            <Grid container spacing={2}>
                                                <Grid item xs="12" sx={{display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
                                                    <img src={Logo} alt="logo" width="145" style={{ margin: 'auto', height: '48px', width: '102px' }} />
                                                         <Typography sx={{ backgroundColor: '#D52F37', width: '35px', height: '35px', borderRadius: '50%',
                                                            marginBottom: '6px', position: 'relative', top: '20px' }}>
                                                            
                                                            <ClearIcon sx={{color: '#fff', position: 'relative', top: '4px', left: "5px"}}/>
                                                        </Typography>
                                                    <Typography color="#000000" className={classes.login} align="center" variant="h4" sx={{
                                                        fontSize: { md: '22px', lg: '23px !important' },
                                                        marginTop: '20px',
                                                        marginBottom: "15px !important",
                                                        color: '#D52F37',
                                                        fontWeight: 600
                                                    }}>
                                                        Email Verification Failed
                                                    </Typography>
                                                    <Typography sx={{
                                                        textAlign: 'center',
                                                        color: '#000',
                                                        fontSize: '14px',
                                                        marginTop: '10px'
                                                    }}>
                                                        {response || 'It looks like you have verified your email already or the verification link has expired!'}
                                                    </Typography>
                                                </Grid>
                                                {/* <Grid item xs={12}>
                                                    <LoadingButton
                                                        size="medium"
                                                        type="submit"
                                                        variant="contained"
                                                        loading={loading}
                                                        sx={{
                                                            backgroundColor: '#D52F37',
                                                            borderRadius: '30px',
                                                            width: '100%',
                                                            marginTop: '20px',
                                                            '&:hover': {
                                                                backgroundColor: '#D52F37 !important',
                                                            }
                                                        }}
                                                        onClick={handleGoHome}
                                                    >
                                                        Try Again
                                                    </LoadingButton>
                                                </Grid> */}
                                                <Grid item xs={12}>
                                                    <Button
                                                        sx={{
                                                            color: " #004222ED !important",
                                                            width: '100%',
                                                            marginTop: '8px',
                                                            '&:hover': { background: 'none !important' }
                                                        }}
                                                        onClick={BackToLogIn}
                                                    >
                                                        <span style={{
                                                            fontSize: "15px",
                                                            fontWeight: '600',
                                                            marginRight: '5px',
                                                            marginBottom: '2px'
                                                        }}>
                                                            <img src={backArrow} alt="Back" style={{
                                                                width: "22px",
                                                                height: "20px",
                                                                position: 'relative',
                                                                right: '4px',
                                                                top: '2px'
                                                            }} />
                                                        </span>
                                                        {t('Back to Login')}
                                                    </Button>
                                                </Grid>
                                            </Grid>
                                        </Box>
                                    )}
                                </Box>
                            ) : (
                                <div
                                    style={{
                                        position: 'absolute',
                                        width: '80%',
                                        height: `calc(100vh - 70px)`,
                                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                        display: 'flex',
                                        flexFlow: 'column',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                >
                                    <LottieLoading loading={loading} />
                                </div>
                            )}
                        </Grid>
                    
                        <Grid id="hideSlider" sx={{padding: '0px !important'}} item xs={12} md={7.5}>
                            <Box id="hideSliderBoxs" sx={{ height: `${loginFormHeight}px`, display: 'flex' }}>
                                <Carousel
                                    autoPlay
                                    interval={7000}
                                    showArrows={false}
                                    showStatus={false}
                                    axis='horizontal'
                                    infiniteLoop
                                    showThumbs={false}
                                    showIndicators={false}
                                    className={classes.carouselContainer}
                                    sx={{ minHeight: '500px' }}
                                >
                                    {mailSentImages.map((image, idx) => (
                                        <div key={idx} style={{ minHeight: '100%' }}>
                                            <img
                                                className={classes.slideImg}
                                                alt={`img ${idx + 1}`}
                                                src={image}
                                                style={{ width: '100%', borderRadius: '15px', minHeight: '100%' }}
                                            />
                                        </div>
                                    ))}
                                </Carousel>
                            </Box>
                        </Grid>

                    
                    </Grid>
                </Container>
                <Dialog open={isModalOpen} onClose={closeModal}>
          <DialogTitle>{t("Verify your email.")}</DialogTitle>
          <DialogContent>
          <p>{t("We have sent an email to")} {''}<strong>{emailID}</strong>{''}.</p>
          <p>{t("Please verify your email and login!")}</p>         
           </DialogContent>
          <DialogActions>
            <LoadingButton
              size="medium"
              type="submit"
              variant="contained"
              sx={{ backgroundColor: '#345CA1', borderRadius: '5px' }}
              onClick={closeModal}
            >
              {t("Go back To Login")}
            </LoadingButton>

          </DialogActions>
        </Dialog>
            </Page>

            <SnackBar
                open={openSnackbar}
                snackbarTitle={snackbarTitle}
                close={() => setOpenSnackbar(false)}
            />
        </div>
    );
};

export default EmailVerification;
